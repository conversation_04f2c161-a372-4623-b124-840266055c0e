FROM node:20-alpine AS builder

WORKDIR /usr/src/app

# Copy package files
COPY package*.json ./
COPY pnpm-lock.yaml ./

# Install pnpm
RUN npm install -g pnpm

# Install ALL dependencies (including devDependencies)
RUN pnpm install --frozen-lockfile

# Copy source
COPY . .

# Build the application
RUN pnpm run build

# Production stage
FROM node:20-alpine
RUN apk add --no-cache curl

WORKDIR /usr/src/app

# Copy package files
COPY --from=builder /usr/src/app/package*.json ./
COPY --from=builder /usr/src/app/pnpm-lock.yaml ./

# Copy built application
COPY --from=builder /usr/src/app/dist ./dist
# Copy node_modules containing the production dependencies
COPY --from=builder /usr/src/app/node_modules ./node_modules

# Install pnpm (needed for production)
RUN npm install -g pnpm

# Clean up
RUN rm -rf /var/lib/apt/lists/*

# Set production environment
ENV NODE_ENV=production

# Expose port
EXPOSE 3000

# Start the application
CMD [ "node", "dist/main" ]
