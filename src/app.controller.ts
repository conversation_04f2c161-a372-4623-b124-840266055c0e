import { Body, Controller, Get, Post } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

import env from './config/env.config';
import { VersionDto } from './dto/check-app-version.dto';
import { HealthCheckResponseDto } from './dto/health-check-response.dto';

const version = process.env.npm_package_version || '1.0.0';

@ApiTags('System')
@Controller()
export class AppController {
  @Get()
  @ApiOperation({
    summary: 'Health check',
    description: 'Check if the API is running',
  })
  @ApiResponse({
    status: 200,
    description: 'API is running',
    type: HealthCheckResponseDto,
  })
  healthCheck() {
    return {
      status: 'ok',
      version,
    };
  }

  @Post('/version')
  @ApiOperation({
    summary: 'Check API version',
    description: 'Check if the API is running on the correct version',
  })
  @ApiResponse({
    status: 200,
    description: 'API is running on the correct version',
    type: Boolean,
  })
  checkVersion(@Body() versionDto: VersionDto) {
    return compareVersions(versionDto.version, env.CHECK_VERSION) >= 0;
  }
}

function compareVersions(version1: string, version2: string): number {
  const v1Parts = version1.split('.').map(Number);
  const v2Parts = version2.split('.').map(Number);

  for (let i = 0; i < Math.max(v1Parts.length, v2Parts.length); i++) {
    const v1Part = v1Parts[i] || 0;
    const v2Part = v2Parts[i] || 0;

    if (v1Part > v2Part) return 1;
    if (v1Part < v2Part) return -1;
  }

  return 0;
}
