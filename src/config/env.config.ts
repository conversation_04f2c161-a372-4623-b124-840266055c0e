import fs from 'node:fs';
import path from 'node:path';

import { config } from 'dotenv';
import { expand } from 'dotenv-expand';
import { z } from 'zod';

function getEnvValue(key: string): string | undefined {
  const fileEnvKey = `${key}_FILE`;
  const secretPath = process.env[fileEnvKey];

  if (secretPath && fs.existsSync(secretPath)) {
    try {
      return fs.readFileSync(secretPath, 'utf8').trim();
    } catch (error) {
      console.warn(`Failed to read secret from ${secretPath}`);
      console.warn(error);
    }
  }

  return process.env[key];
}

expand(
  config({
    path: path.resolve(
      process.cwd(),
      process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
    ),
  }),
);

const envWithSecrets = {
  ...process.env,
  DATABASE_URL: getEnvValue('DATABASE_URL'),
  JWT_SECRET: getEnvValue('JWT_SECRET'),
  JWT_REFRESH_TOKEN_SECRET: getEnvValue('JWT_REFRESH_TOKEN_SECRET'),
  S3_ACCESS_KEY_ID: getEnvValue('S3_ACCESS_KEY_ID'),
  S3_SECRET_ACCESS_KEY: getEnvValue('S3_SECRET_ACCESS_KEY'),
  FIREBASE_CLIENT_EMAIL: getEnvValue('FIREBASE_CLIENT_EMAIL'),
  FIREBASE_PRIVATE_KEY: getEnvValue('FIREBASE_PRIVATE_KEY'),
  MAILER_PASS: getEnvValue('MAILER_PASS'),
  REDIS_PASSWORD: getEnvValue('REDIS_PASSWORD'),
  HASH_SALT: getEnvValue('HASH_SALT'),
};

const EnvSchema = z
  .object({
    NODE_ENV: z
      .enum(['development', 'production', 'test'])
      .default('development'),
    PORT: z.coerce.number().default(3000),

    // Database
    DATABASE_URL: z.string().url(),

    // Auth
    JWT_SECRET: z.string().min(1),
    JWT_REFRESH_TOKEN_SECRET: z.string().min(1),

    // S3
    S3_BUCKET: z.string().min(1),
    S3_ACCESS_KEY_ID: z.string().min(1),
    S3_SECRET_ACCESS_KEY: z.string().min(1),

    // Firebase
    FIREBASE_PROJECT_ID: z.string().min(1),
    FIREBASE_CLIENT_EMAIL: z.string().email(),
    FIREBASE_PRIVATE_KEY: z.string().min(1),

    // Email
    MAILER_HOST: z.string().min(1),
    MAILER_PORT: z.coerce.number(),
    MAILER_USER: z.string().email(),
    MAILER_PASS: z.string().min(1),
    MAILER_FROM: z.string().min(1),

    // Redis
    REDIS_HOST: z.string().min(1),
    REDIS_PORT: z.coerce.number(),
    REDIS_PASSWORD: z.string().min(1),

    // Hash
    HASH_SALT: z.string().min(1),
    CHECK_VERSION: z.string().min(1),
  })
  .superRefine((input, ctx) => {
    if (input.NODE_ENV === 'production') {
      // Add production-specific validations
      if (!input.DATABASE_URL.startsWith('postgresql://')) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Production DATABASE_URL must use postgresql://',
          path: ['DATABASE_URL'],
        });
      }
    }
  })
  .transform((val) => ({
    ...val,
    isDevelopment: val.NODE_ENV === 'development',
    isProduction: val.NODE_ENV === 'production',
    isTest: val.NODE_ENV === 'test',
  }));

export type EnvType = z.infer<typeof EnvSchema>;

const { success, data: env, error } = EnvSchema.safeParse(envWithSecrets);

if (!success) {
  console.error('❌ Invalid environment variables:');
  console.error(JSON.stringify(error.flatten().fieldErrors, null, 2));
  process.exit(1);
}

export default env!;
