import { createInsertSchema } from 'drizzle-zod';
import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

import { users } from '@/modules/db/entities/user.entity';

export const updateUserSchema = createInsertSchema(users).partial();

export const updatePersonalInfoSchema = createInsertSchema(users)
  .omit({
    id: true,
    hashedPassword: true,
    isEmailVerified: true,
    isPhoneVerified: true,
    role: true,
    createdAt: true,
    email: true,
    // phoneNumber: true,
  })
  .partial();

export const updateUserEmailSchema = createInsertSchema(users)
  .pick({
    email: true,
  })
  .extend({
    password: z.string().min(8).describe('Password (min 8 characters)'),
  });

export class UpdateUserDto extends createZodDto(updateUserSchema) {}

export class UpdatePersonalInfoDto extends createZodDto(
  updatePersonalInfoSchema,
) {}

export class UpdateUserEmailDto extends createZodDto(updateUserEmailSchema) {}
