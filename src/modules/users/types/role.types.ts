import { ApprovalState, EmploymentStatus, Role } from '@/common/enums';
import { ManagerPermissionType } from '@/common/permissions/manager-permissions.config';
import { WorkerDto } from '@/modules/workers/dto/worker.dto';

export type BaseRoleInfo = {
  type: Role;
  entityId: string | null;
};

export type ManagerRoleInfo = BaseRoleInfo & {
  type: Role.Manager;
  entityId: string;
  approvalState: ApprovalState | null;
  permissionType: ManagerPermissionType | null;
  hasProjects: boolean;
  employmentStatus: EmploymentStatus | null;
};

export type WorkerRoleInfo = BaseRoleInfo & {
  type: Role.Worker;
  entityId: string | null;
  independent: boolean;
  approvalState: ApprovalState | null;
  employmentStatus: EmploymentStatus | null;
};

export type PartnerRoleInfo = BaseRoleInfo & {
  type: Role.Partner;
  entityId: string;
};

export type RoleInfo = ManagerRoleInfo | WorkerRoleInfo | PartnerRoleInfo;

export type WorkerStatus = {
  activeWorker?: WorkerDto | null;
  pendingWorker?: WorkerDto | null;
  latestWorker?: WorkerDto | null;
  effectiveWorker?: WorkerDto | null;
};
