import { Inject, Injectable } from '@nestjs/common';
import { and, eq } from 'drizzle-orm';

import { Database } from '../db/db.module';
import { CreateFilePermissionDto } from './dto/create-file-permission.dto';
import { filePermissions } from '../db/entities/file-permission.entity';

@Injectable()
export class FilePermissionsService {
  constructor(@Inject('DB') private readonly db: Database) {}
  create(...createFilePermissionDtos: CreateFilePermissionDto[]) {
    return this.db.insert(filePermissions).values(createFilePermissionDtos);
  }

  remove(fileId: string, userId: string) {
    return this.db
      .delete(filePermissions)
      .where(
        and(
          eq(filePermissions.fileId, fileId),
          eq(filePermissions.userId, userId),
        ),
      );
  }
}
