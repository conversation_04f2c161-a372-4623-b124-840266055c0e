import { Controller } from '@nestjs/common';

import { FilePermissionsService } from './file-permissions.service';

@Controller('file-permissions')
export class FilePermissionsController {
  constructor(
    private readonly filePermissionsService: FilePermissionsService,
  ) {}

  // @Post()
  // create(@Body() createFilePermissionDto: CreateFilePermissionDto) {
  //   return this.filePermissionsService.create(createFilePermissionDto);
  // }

  // @Get()
  // findAll() {
  //   return this.filePermissionsService.findAll();
  // }

  // @Get(':id')
  // findOne(@Param('id') id: string) {
  //   return this.filePermissionsService.findOne(+id);
  // }

  // @Patch(':id')
  // update(
  //   @Param('id') id: string,
  //   @Body() updateFilePermissionDto: UpdateFilePermissionDto,
  // ) {
  //   return this.filePermissionsService.update(+id, updateFilePermissionDto);
  // }

  // @Delete(':id')
  // remove(@Param('id') id: string) {
  //   return this.filePermissionsService.remove(+id);
  // }
}
