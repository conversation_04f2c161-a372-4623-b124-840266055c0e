import { Module, forwardRef } from '@nestjs/common';

import { WorkersController } from './workers.controller';
import { WorkersService } from './workers.service';
import { DailyReportsModule } from '../daily-reports/daily-reports.module';
import { EmploymentHistoryModule } from '../employment-history/employment-history.module';
import { ManagersModule } from '../managers/managers.module';
import { NotificationsModule } from '../notifications/notifications.module';
import { RegistrationCodesModule } from '../registration-codes/registration-codes.module';
import { RegistrationRequestsModule } from '../registration-requests/registration-requests.module';
import { UpdatesModule } from '../updates/updates.module';
import { UsersModule } from '../users/users.module';

@Module({
  imports: [
    forwardRef(() => DailyReportsModule),
    forwardRef(() => RegistrationCodesModule),
    forwardRef(() => RegistrationRequestsModule),
    forwardRef(() => UsersModule),
    forwardRef(() => ManagersModule),
    EmploymentHistoryModule,
    forwardRef(() => UpdatesModule),
    forwardRef(() => NotificationsModule),
  ],
  controllers: [WorkersController],
  providers: [WorkersService],
  exports: [WorkersService],
})
export class WorkersModule {}
