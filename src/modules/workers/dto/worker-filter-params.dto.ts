import { ApiProperty } from '@nestjs/swagger';
import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

import { stringToBoolean } from '@/common/utils/zodBooleanQueryParam';

const projectIdTransformer = z.string().transform((value) => {
  const includes: string[] = [];
  const excludes: string[] = [];

  value.split(',').forEach((id) => {
    const trimmed = id.trim();
    if (trimmed.startsWith('!')) {
      excludes.push(trimmed.slice(1));
    } else {
      includes.push(trimmed);
    }
  });

  return { includes, excludes };
});

const fullNameTransformer = z.string().transform((value) => {
  return value.split(' ');
});

export const workerFilterParamsSchema = z.object({
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  fullName: fullNameTransformer.optional(),
  projects: projectIdTransformer.optional(),
  withoutProject: stringToBoolean().optional(),
  withProject: stringToBoolean().optional(),
  status: z.enum(['on-duty', 'present', 'absent']).optional(),
});

export class WorkerFilterParamsDto extends createZodDto(
  workerFilterParamsSchema,
) {
  @ApiProperty({
    description: 'Filter by worker first name or search term',
    required: false,
    example: 'John',
  })
  firstName?: string;

  @ApiProperty({
    description: 'Filter by worker last name or search term',
    required: false,
    example: 'Doe',
  })
  lastName?: string;

  @ApiProperty({
    description: 'Filter by worker full name or search term',
    required: false,
    example: 'John Doe',
  })
  fullName?: string[];

  @ApiProperty({
    description:
      'Filter by project IDs. Use comma-separated values. Prefix with ! to exclude',
    required: false,
    example: 'proj1,!proj2,proj3',
  })
  projects?: {
    includes: string[];
    excludes: string[];
  };

  @ApiProperty({
    description: 'Filter workers that are not assigned to any project',
    required: false,
    type: Boolean,
    example: 'true',
  })
  withoutProject?: boolean;

  @ApiProperty({
    description: 'Filter workers that are assigned to any project',
    required: false,
    type: Boolean,
    example: 'true',
  })
  withProject?: boolean;

  @ApiProperty({
    description: 'Filter workers by their current working status',
    required: false,
    enum: ['on-duty', 'present', 'absent'],
    example: 'on-duty',
  })
  status?: 'on-duty' | 'present' | 'absent';
}
