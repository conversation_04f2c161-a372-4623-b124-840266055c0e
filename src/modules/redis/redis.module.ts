import {
  RedisModule as RedisModuleImport,
  RedisModuleOptions,
} from '@liaoliaots/nestjs-redis';
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';

@Module({
  imports: [
    RedisModuleImport.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      // @ts-expect-error ts(2322)
      useFactory: (
        ...[configService]: [ConfigService]
      ): RedisModuleOptions => ({
        config: {
          host: configService.get('redis.host'),
          port: configService.get('redis.port'),
          password: configService.get('redis.password'),
        },
      }),
    }),
  ],
})
export class RedisModule {}
