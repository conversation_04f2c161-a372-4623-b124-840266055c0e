import {
  Body,
  Controller,
  HttpStatus,
  NotFoundException,
  Param,
  Post,
  Put,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';

import { User } from '@/common/decorators/user.decorator';

import { DevicesService } from './devices.service';
import { CreateDeviceDto } from './dto/create-device.dto';
import { DeviceRegistrationResponseDto } from './dto/device-registration.dto';
import { RequestUserType } from '../auth/dto/request-user.dto';
import { JwtAuthGuard } from '../auth/guards/jwt.guard';

@ApiTags('Devices')
@Controller('devices')
export class DevicesController {
  constructor(private readonly devicesService: DevicesService) {}

  @Post('register')
  @ApiOperation({
    summary: 'Register device',
    description: 'Register a new device or update existing FCM token',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Device registered successfully',
    type: DeviceRegistrationResponseDto,
  })
  async registerDevice(
    @Body() deviceDto: CreateDeviceDto,
  ): Promise<DeviceRegistrationResponseDto> {
    const existingDevice = await this.devicesService.findByToken(
      deviceDto.token,
    );

    if (existingDevice) {
      const device = await this.devicesService.update(
        existingDevice.id,
        deviceDto,
      );

      return {
        id: device.id,
        token: device.token,
        platform: device.platform,
        name: device.name,
      };
    }

    const device = await this.devicesService.create(deviceDto);

    return {
      id: device.id,
      token: device.token,
      platform: device.platform,
      name: device.name,
    };
  }

  @Put(':id/token')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Update device FCM token',
    description: 'Update FCM token for existing device',
  })
  async updateToken(
    @User() user: RequestUserType,
    @Param('id') deviceId: string,
    @Body('token') newToken: string,
  ) {
    const device = await this.devicesService.findOne(deviceId);

    if (!device || device.userId !== user.id) {
      throw new NotFoundException('Device not found');
    }

    const existingDeviceWithToken =
      await this.devicesService.findByToken(newToken);
    if (existingDeviceWithToken && existingDeviceWithToken.id !== deviceId) {
      await this.devicesService.update(
        existingDeviceWithToken.id,
        {
          isActive: false,
        },
        user.id,
      );
    }
  }
}
