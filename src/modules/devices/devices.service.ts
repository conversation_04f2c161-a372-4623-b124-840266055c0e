import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { and, eq, inArray, isNull, lt } from 'drizzle-orm';

import { Database } from '../db/db.module';
import { CreateDeviceDto } from './dto/create-device.dto';
import { UpdateDeviceDto } from './dto/update-device.dto';
import { devices } from '../db/entities/device.entity';

@Injectable()
export class DevicesService {
  constructor(@Inject('DB') private readonly db: Database) {}

  async create(createDeviceDto: CreateDeviceDto, userId?: string) {
    return (
      await this.db
        .insert(devices)
        .values({
          ...createDeviceDto,
          userId,
        })
        .returning()
    )[0];
  }

  async claimDevice(deviceId: string, userId: string) {
    return (
      await this.db
        .update(devices)
        .set({
          userId,
          lastUsedAt: new Date(),
        })
        .where(eq(devices.id, deviceId))
        .returning()
    )[0];
  }

  findOne(id: string) {
    return this.db.query.devices.findFirst({
      where: (devices, { eq, and }) =>
        and(eq(devices.id, id), eq(devices.isActive, true)),
    });
  }

  findAllByUserId(userId: string) {
    return this.db.query.devices.findMany({
      where: (devices, { eq, and }) =>
        and(eq(devices.userId, userId), eq(devices.isActive, true)),
    });
  }

  findByToken(token: string) {
    return this.db.query.devices.findFirst({
      where: (devices, { eq, and }) =>
        and(eq(devices.token, token), eq(devices.isActive, true)),
    });
  }

  update(id: string, updateDeviceDto: UpdateDeviceDto, userId?: string) {
    return this.db.transaction(async (tx) => {
      const device = tx.query.devices.findFirst({
        where: (devices, { eq, and }) =>
          and(
            eq(devices.id, id),
            eq(devices.isActive, true),
            userId ? eq(devices.userId, userId) : undefined,
          ),
      });

      if (!device) {
        throw new NotFoundException('Device not found');
      }

      return (
        await tx
          .update(devices)
          .set(updateDeviceDto)
          .where(eq(devices.id, id))
          .returning()
      )[0];
    });
  }

  async deactivate(token: string, userId: string) {
    const device = await this.findByToken(token);
    if (!device) throw new NotFoundException('Device not found');

    return this.update(device.id, { isActive: false }, userId);
  }

  async disownDevice(deviceId: string, userId: string) {
    return this.db
      .update(devices)
      .set({ userId: null })
      .where(and(eq(devices.id, deviceId), eq(devices.userId, userId)));
  }

  updateLastUsedAt(ids: string[] | string, date?: Date) {
    return this.db
      .update(devices)
      .set({ lastUsedAt: date || new Date() })
      .where(inArray(devices.id, Array.isArray(ids) ? ids : [ids]));
  }

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async cleanupInactiveDevicesWithoutUser() {
    const sixtyDaysAgo = new Date();
    sixtyDaysAgo.setDate(sixtyDaysAgo.getDate() - 60);

    return this.db
      .update(devices)
      .set({ isActive: false })
      .where(
        and(
          eq(devices.isActive, true),
          lt(devices.lastUsedAt, sixtyDaysAgo),
          isNull(devices.userId),
        ),
      );
  }

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async disownOldDevices() {
    const threeMonthsAgo = new Date();
    threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);

    return this.db
      .update(devices)
      .set({ userId: null })
      .where(
        and(eq(devices.isActive, true), lt(devices.lastUsedAt, threeMonthsAgo)),
      );
  }
}
