import { Module, forwardRef } from '@nestjs/common';

import { ManagersController } from './managers.controller';
import { ManagersService } from './managers.service';
import { ActivityHistoryModule } from '../activity-history/activity-history.module';
import { EmploymentHistoryModule } from '../employment-history/employment-history.module';
import { NotificationsModule } from '../notifications/notifications.module';
import { PartnersModule } from '../partners/partners.module';
import { ProjectsModule } from '../projects/projects.module';
import { UpdatesModule } from '../updates/updates.module';
import { UsersModule } from '../users/users.module';
import { WorkersModule } from '../workers/workers.module';

@Module({
  imports: [
    WorkersModule,
    forwardRef(() => ProjectsModule),
    forwardRef(() => PartnersModule),
    forwardRef(() => UsersModule),
    forwardRef(() => UpdatesModule),
    forwardRef(() => NotificationsModule),
    forwardRef(() => EmploymentHistoryModule),
    forwardRef(() => ActivityHistoryModule),
  ],
  controllers: [ManagersController],
  providers: [ManagersService],
  exports: [ManagersService],
})
export class ManagersModule {}
