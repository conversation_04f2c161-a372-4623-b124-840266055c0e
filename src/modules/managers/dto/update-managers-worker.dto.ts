import { createInsertSchema } from 'drizzle-zod';
import { createZodDto } from 'nestjs-zod';

import { workers } from '@/modules/db/entities/worker.entity';

export const updateManagersWorkerSchema = createInsertSchema(workers)
  .pick({
    profession: true,
    hourlyRate: true,
    professionId: true,
  })
  .partial();

export class UpdateManagersWorkerDto extends createZodDto(
  updateManagersWorkerSchema,
) {}
