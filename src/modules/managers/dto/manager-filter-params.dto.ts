import { ApiProperty } from '@nestjs/swagger';
import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

import { ApprovalState } from '@/common/enums';
import { ManagerPermissionType } from '@/common/permissions/manager-permissions.config';

const fullNameTransformer = z.string().transform((value) => value.split(' '));

export const managerFilterParamsSchema = z.object({
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  fullName: fullNameTransformer.optional(),
  permissionType: z.nativeEnum(ManagerPermissionType).optional(),
  approvalState: z.nativeEnum(ApprovalState).optional(),
  isActive: z.boolean().optional(),
});

export class ManagerFilterParamsDto extends createZodDto(
  managerFilterParamsSchema,
) {
  @ApiProperty({
    description: 'Filter by manager first name',
    required: false,
    example: '<PERSON>',
  })
  firstName?: string;

  @ApiProperty({
    description: 'Filter by manager last name',
    required: false,
    example: 'Doe',
  })
  lastName?: string;

  @ApiProperty({
    description: 'Filter by manager full name',
    required: false,
    example: 'John Doe',
  })
  fullName?: string[];

  @ApiProperty({
    description: 'Filter by permission type',
    required: false,
    enum: ManagerPermissionType,
    example: ManagerPermissionType.All,
  })
  permissionType?: ManagerPermissionType;

  @ApiProperty({
    description: 'Filter by approval state',
    required: false,
    enum: ApprovalState,
    example: ApprovalState.Approved,
  })
  approvalState?: ApprovalState;

  @ApiProperty({
    description: 'Filter by active status',
    required: false,
    type: Boolean,
    example: true,
  })
  isActive?: boolean;
}
