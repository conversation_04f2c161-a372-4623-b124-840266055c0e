import { createInsertSchema } from 'drizzle-zod';
import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

import { ManagerPermissionType } from '@/common/permissions/manager-permissions.config';
import { managers } from '@/modules/db/entities/manager.entity';

const baseSchema = createInsertSchema(managers);

export const createManagerSchema = baseSchema.extend({
  permissionType: z
    .enum([ManagerPermissionType.All, ManagerPermissionType.ProjectManager])
    .default(ManagerPermissionType.ProjectManager),
});

export class CreateManagerDto extends createZodDto(createManagerSchema) {}
