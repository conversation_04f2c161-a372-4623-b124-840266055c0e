import {
  BadRequestException,
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  NotFoundException,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiForbiddenResponse,
  ApiNotFoundResponse,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';

import { Roles } from '@/common/decorators/roles.decorator';
import { User } from '@/common/decorators/user.decorator';
import { Role } from '@/common/enums';
import { RolesGuard } from '@/common/guards/roles.guard';

import { DailyReportsService } from './daily-reports.service';
import { ChangeApprovedHoursDto } from './dto/change-approved-hours.dto';
import { CreateManualReportDto } from './dto/create-manual-report.dto';
import {
  DailyReportWithPhotosDto,
  DailyReportWithValidationsDto,
} from './dto/daily-report.dto';
import { ReportFilterParamsDto } from './dto/daily-reports-filter-params.dto';
import {
  FinishReportDto,
  ReportStateChangeResponseDto,
} from './dto/finish-report.dto';
import { ResolveReportsDto } from './dto/resolve-reports.dto';
import { StartReportDto, StartReportResponseDto } from './dto/start-report.dto';
import { RequestUserType } from '../auth/dto/request-user.dto';
import { JwtAuthGuard } from '../auth/guards/jwt.guard';

@ApiTags('Daily Reports')
@ApiBearerAuth()
@Controller('daily-reports')
@UseGuards(JwtAuthGuard, RolesGuard)
export class DailyReportsController {
  constructor(private readonly dailyReportsService: DailyReportsService) {}

  @Get()
  @ApiOperation({
    summary: 'List daily reports',
    description: 'Retrieve all daily reports with optional filtering',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Reports retrieved successfully',
    type: [DailyReportWithPhotosDto],
  })
  @ApiUnauthorizedResponse({ description: 'Not authenticated' })
  @ApiForbiddenResponse({ description: 'Not authorized to view reports' })
  findAll(
    @User() user: RequestUserType,
    @Query() filterParams?: ReportFilterParamsDto,
  ): Promise<DailyReportWithPhotosDto[]> {
    return this.dailyReportsService.findAll(user, filterParams);
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Get report details',
    description: 'Retrieve a specific daily report with validations',
  })
  @ApiParam({
    name: 'id',
    description: 'Report unique identifier',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Report retrieved successfully',
    type: DailyReportWithValidationsDto,
  })
  @ApiNotFoundResponse({ description: 'Report not found' })
  async findOne(
    @Param('id') id: string,
  ): Promise<DailyReportWithValidationsDto> {
    const report = await this.dailyReportsService.findOne(id);
    if (!report) throw new NotFoundException('Report not found');
    return report;
  }

  @Post('start')
  @Roles({
    type: Role.Worker,
    settings: {
      mustBeEmployed: true,
      mustBeApproved: true,
    },
  })
  @ApiOperation({
    summary: 'Start new report',
    description: 'Start a new daily report for the current worker',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Report started successfully',
    type: StartReportResponseDto,
  })
  @ApiBadRequestResponse({
    description: 'Invalid report data or active report exists',
  })
  @ApiForbiddenResponse({ description: 'Not authorized to start report' })
  startReport(
    @Body() startReportDto: StartReportDto,
    @User() user: RequestUserType,
  ): Promise<StartReportResponseDto> {
    return this.dailyReportsService.startReport(startReportDto, user.entityId);
  }

  @Post(':id/finish')
  @HttpCode(HttpStatus.OK)
  @Roles(
    {
      type: Role.Worker,
      settings: {
        mustBeEmployed: true,
        mustBeApproved: true,
      },
    },
    Role.Partner,
    Role.Manager,
  )
  @ApiOperation({
    summary: 'Finish report',
    description: 'Complete an active daily report',
  })
  @ApiParam({
    name: 'id',
    description: 'Report unique identifier',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Report finished successfully',
    type: ReportStateChangeResponseDto,
  })
  @ApiBadRequestResponse({ description: 'Invalid report state or data' })
  @ApiNotFoundResponse({ description: 'Report not found' })
  async finishReport(
    @Param('id') id: string,
    @Body() finishReportDto: FinishReportDto,
    @User() user: RequestUserType,
  ): Promise<ReportStateChangeResponseDto> {
    return this.dailyReportsService.finishReport(id, finishReportDto, user);
  }

  @Patch(':id/pause')
  @Roles({
    type: Role.Worker,
    settings: {
      mustBeEmployed: true,
      mustBeApproved: true,
    },
  })
  @ApiOperation({
    summary: 'Pause report',
    description: 'Pause an active daily report',
  })
  @ApiParam({
    name: 'id',
    description: 'Report unique identifier',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Report paused successfully',
    type: ReportStateChangeResponseDto,
  })
  @ApiBadRequestResponse({ description: 'Invalid report state' })
  @ApiNotFoundResponse({ description: 'Report not found' })
  changePauseState(
    @Param('id') id: string,
    @User() user: RequestUserType,
  ): Promise<ReportStateChangeResponseDto> {
    return this.dailyReportsService.changePauseState(
      id,
      { onPause: true },
      user.entityId,
    );
  }

  @Patch(':id/unpause')
  @Roles({
    type: Role.Worker,
    settings: {
      mustBeEmployed: true,
      mustBeApproved: true,
    },
  })
  @ApiOperation({
    summary: 'Resume report',
    description: 'Resume a paused daily report',
  })
  @ApiParam({
    name: 'id',
    description: 'Report unique identifier',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Report resumed successfully',
    type: ReportStateChangeResponseDto,
  })
  @ApiBadRequestResponse({ description: 'Invalid report state' })
  @ApiNotFoundResponse({ description: 'Report not found' })
  unpauseReport(
    @Param('id') id: string,
    @User() user: RequestUserType,
  ): Promise<ReportStateChangeResponseDto> {
    return this.dailyReportsService.changePauseState(
      id,
      { onPause: false },
      user.entityId,
    );
  }

  @Patch(':id/approve')
  @Roles(Role.Partner, Role.Manager)
  @ApiOperation({
    summary: 'Approve report',
    description: 'Approve a submitted daily report',
  })
  @ApiParam({
    name: 'id',
    description: 'Report unique identifier',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Report approved successfully',
  })
  @ApiBadRequestResponse({ description: 'Invalid report state' })
  @ApiNotFoundResponse({ description: 'Report not found' })
  approveReport(
    @Param('id') id: string,
    @User() user: RequestUserType,
    @Body() changeApprovedHoursDto: ChangeApprovedHoursDto,
  ) {
    return this.dailyReportsService.changeReportStatus(
      id,
      user,
      'approved',
      changeApprovedHoursDto,
    );
  }

  @Patch(':id/reject')
  @Roles(Role.Partner, Role.Manager)
  @ApiOperation({
    summary: 'Reject report',
    description: 'Reject a submitted daily report',
  })
  @ApiParam({
    name: 'id',
    description: 'Report unique identifier',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Report rejected successfully',
  })
  @ApiBadRequestResponse({ description: 'Invalid report state' })
  @ApiNotFoundResponse({ description: 'Report not found' })
  rejectReport(@Param('id') id: string, @User() user: RequestUserType) {
    return this.dailyReportsService.changeReportStatus(id, user, 'declined');
  }

  @Patch('/resolve')
  @Roles(Role.Partner, Role.Manager)
  @ApiOperation({
    summary: 'Bulk resolve reports',
    description: 'Approve or reject multiple reports at once',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Reports resolved successfully',
  })
  @ApiBadRequestResponse({ description: 'Invalid report states or IDs' })
  resolveReports(
    @Body() resolveReportsDto: ResolveReportsDto,
    @User() user: RequestUserType,
  ) {
    return this.dailyReportsService.changeReportStatus(
      resolveReportsDto.ids,
      user,
      resolveReportsDto.status,
    );
  }

  @Post('manual')
  @Roles(
    Role.Partner,
    {
      type: Role.Worker,
      settings: {
        mustBeApproved: true,
        mustBeEmployed: true,
      },
    },
    Role.Manager,
  )
  @ApiOperation({
    summary: 'Create manual report',
    description: 'Create a manual daily report entry',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Manual report created successfully',
  })
  @ApiBadRequestResponse({ description: 'Invalid report data' })
  @ApiForbiddenResponse({
    description: 'Not authorized to create manual reports',
  })
  createManualReport(
    @Body() createManualReportDto: CreateManualReportDto,
    @User() user: RequestUserType,
  ) {
    if (user.role === Role.Worker && createManualReportDto.workerId) {
      throw new BadRequestException(
        'Worker ID must not be specified for worker reports',
      );
    } else if (
      (user.role === Role.Partner || user.role === Role.Manager) &&
      !createManualReportDto.workerId
    ) {
      throw new BadRequestException('Worker ID must be specified');
    }
    const workerId =
      (user.role === Role.Partner || user.role === Role.Manager) &&
      createManualReportDto.workerId
        ? createManualReportDto.workerId
        : user.entityId;

    return this.dailyReportsService.createManualReport(
      createManualReportDto,
      workerId,
      user,
    );
  }
}
