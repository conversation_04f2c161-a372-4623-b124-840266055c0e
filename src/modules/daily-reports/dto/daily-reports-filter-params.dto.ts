import { ApiProperty } from '@nestjs/swagger';
import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

import { stringToBoolean } from '@/common/utils/zodBooleanQueryParam';

type Status = 'pending' | 'submitted' | 'approved' | 'declined';

export const reportFilterParamsSchema = z.object({
  status: z
    .string()
    .transform((value) => {
      const statuses: string[] = [];
      value.split(',').forEach((status) => {
        if (!['pending', 'submitted', 'approved', 'declined'].includes(status))
          return;

        const trimmed = status.trim();
        if (trimmed.startsWith('!')) {
          statuses.push(trimmed.slice(1));
        } else {
          statuses.push(trimmed);
        }
      });

      if (statuses.length === 1) return statuses[0] as Status;
      return statuses as Status[];
    })
    .optional(),
  dateFrom: z.string().optional(),
  dateTo: z.string().optional(),
  workerFullName: z.string().optional(),
  includesPhoto: stringToBoolean().optional(),
  includesGeo: stringToBoolean().optional(),
  sortBy: z
    .enum(['date', 'submittedHours', 'approvedHours', 'status'])
    .optional(),
  sortOrder: z.enum(['asc', 'desc']).optional(),
});

export class ReportFilterParamsDto extends createZodDto(
  reportFilterParamsSchema,
) {
  @ApiProperty({
    description: 'Filter reports by their status or statuses',
    required: false,
    enum: ['pending', 'submitted', 'approved', 'declined'],
    example: 'approved | pending, submitted',
  })
  status?: 'pending' | 'submitted' | 'approved' | 'declined' | Status[];

  @ApiProperty({
    description: 'Filter reports from this date (inclusive)',
    required: false,
    example: '2024-01-01',
    pattern: '^d{4}-d{2}-d{2}$',
  })
  dateFrom?: string;

  @ApiProperty({
    description: 'Filter reports until this date (inclusive)',
    required: false,
    example: '2024-01-31',
    pattern: '^d{4}-d{2}-d{2}$',
  })
  dateTo?: string;

  @ApiProperty({
    description: 'Search reports by worker name',
    required: false,
    example: 'John Doe',
  })
  workerFullName?: string;

  @ApiProperty({
    description: 'Filter reports that include photos',
    required: false,
    type: Boolean,
    example: 'true',
  })
  includesPhoto?: boolean;

  @ApiProperty({
    description: 'Filter reports that include geolocation data',
    required: false,
    type: Boolean,
    example: 'true',
  })
  includesGeo?: boolean;

  @ApiProperty({
    description: 'Sort reports by specified property',
    required: false,
    enum: ['date', 'submittedHours', 'approvedHours'],
    example: 'date',
  })
  sortBy?: 'date' | 'submittedHours' | 'approvedHours';

  @ApiProperty({
    description: 'Sort order direction',
    required: false,
    enum: ['asc', 'desc'],
    example: 'desc',
  })
  sortOrder?: 'asc' | 'desc';
}
