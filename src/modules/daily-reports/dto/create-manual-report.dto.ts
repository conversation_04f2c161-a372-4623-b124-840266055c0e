import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

import { startReportSchema } from './start-report.dto';

export const createManualReportSchema = startReportSchema.extend({
  photoId: z.string(),
  submittedHours: z.number(),
  workerId: z.string().optional(),
  manualReason: z.string(),
  endReason: z.string().optional(),
});

export class CreateManualReportDto extends createZodDto(
  createManualReportSchema,
) {}
