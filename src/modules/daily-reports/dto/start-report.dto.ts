import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

import { reportStateChangeResponseSchema } from './finish-report.dto';

export const startReportSchema = z.object({
  reportDate: z.string(),
});

export const startReportResponseSchema = reportStateChangeResponseSchema.extend(
  {
    id: z.string(),
  },
);

export class StartReportDto extends createZodDto(startReportSchema) {}

export class StartReportResponseDto extends createZodDto(
  startReportResponseSchema,
) {}
