import { createSelectSchema } from 'drizzle-zod';
import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

import { dailyReports } from '@/modules/db/entities/daily-report.entity';
import { getPresenceValidationSchema } from '@/modules/presence-validations/dto/presence-validation.dto';

export const getDailyReportSchema = createSelectSchema(dailyReports).omit({
  projectId: true,
});

export const dailyReportSchema = z.object({
  id: z.string(),
  reportDate: z.string(),
  submittedHours: z.string(),
  approvedHours: z.string().nullable(),
  status: z.string(),
  isManual: z.boolean(),
  manualPhotoId: z.string().nullable(),
  createdAt: z.date(),
  authorFullName: z.string(),
  endReason: z.string().nullable(),
  manualReason: z.string().nullable(),
  manualCreatorId: z.string().nullable(),
  manualCreatorRole: z.string().nullable(),
});

export const dailyReportWithPhotosSchema = dailyReportSchema.extend({
  photoAmount: z.number(),
  photos: z.array(z.string()),
});

export const dailyReportWithValidationsSchema = getDailyReportSchema.extend({
  presenceValidations: z.array(getPresenceValidationSchema),
});

export class DailyReportDto extends createZodDto(getDailyReportSchema) {}
export class DailyReportWithValidationsDto extends createZodDto(
  dailyReportWithValidationsSchema,
) {}
export class DailyReportWithPhotosDto extends createZodDto(
  dailyReportWithPhotosSchema,
) {}
