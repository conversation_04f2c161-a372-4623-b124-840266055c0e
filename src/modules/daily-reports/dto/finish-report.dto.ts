import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

export const finishReportSchema = z.object({
  submittedHours: z.number().optional(),
  endReason: z.string().optional(),
});

export const reportStateChangeResponseSchema = z.object({
  presenceValidationId: z.string().optional(),
});

export class FinishReportDto extends createZodDto(finishReportSchema) {}

export class ReportStateChangeResponseDto extends createZodDto(
  reportStateChangeResponseSchema,
) {}
