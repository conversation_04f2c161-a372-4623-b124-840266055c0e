import { Module, forwardRef } from '@nestjs/common';

import { DailyReportsController } from './daily-reports.controller';
import { DailyReportsService } from './daily-reports.service';
import { ManagersModule } from '../managers/managers.module';
import { PauseHistoryModule } from '../pause-history/pause-history.module';
import { PresenceValidationsModule } from '../presence-validations/presence-validations.module';
import { UpdatesModule } from '../updates/updates.module';
import { UsersModule } from '../users/users.module';
import { WorkersModule } from '../workers/workers.module';

@Module({
  imports: [
    forwardRef(() => WorkersModule),
    forwardRef(() => PresenceValidationsModule),
    forwardRef(() => UsersModule),
    forwardRef(() => ManagersModule),
    PauseHistoryModule,
    forwardRef(() => UpdatesModule),
  ],
  controllers: [DailyReportsController],
  providers: [DailyReportsService],
  exports: [DailyReportsService],
})
export class DailyReportsModule {}
