import {
  BadRequestException,
  Body,
  Controller,
  HttpCode,
  HttpStatus,
  Param,
  Patch,
  Post,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiNotFoundResponse,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import { Throttle } from '@nestjs/throttler';

import { AllowUnverifiedEmail } from '@/common/decorators/allow-unverified-email.decorator';
import { User } from '@/common/decorators/user.decorator';

import { CredentialVerificationService } from './credential-verification.service';
import { AutoCreateCredentialVerificationDto } from './dto/create-verification-request.dto';
import { VerifyByCodeDto } from './dto/verify-by-code.dto';
import { RequestUserType } from '../auth/dto/request-user.dto';
import { JwtAuthGuard } from '../auth/guards/jwt.guard';

@ApiTags('Credential Verification')
@Controller('credential-verification')
export class CredentialVerificationController {
  constructor(
    private readonly credentialVerificationService: CredentialVerificationService,
  ) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  @AllowUnverifiedEmail()
  @ApiBearerAuth()
  @Throttle({ default: { limit: 5, ttl: 60000 } })
  @ApiOperation({
    summary: 'Create verification token',
    description:
      'Create a verification token for the given type, invalidates previous tokens of the same type',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Verification token created successfully',
  })
  @ApiBadRequestResponse({
    description: 'Invalid verification type',
  })
  createVerificationToken(
    @User() user: RequestUserType,
    @Body()
    createCredentialVerificationDto: AutoCreateCredentialVerificationDto,
  ) {
    if (createCredentialVerificationDto.type === 'email') {
      return this.credentialVerificationService.createAndSendEmail({
        userId: user.id,
        ...createCredentialVerificationDto,
      });
    } else if (createCredentialVerificationDto.type === 'phone') {
      return this.credentialVerificationService.createAndSendSms({
        userId: user.id,
        ...createCredentialVerificationDto,
      });
    } else {
      throw new BadRequestException('Invalid verification type');
    }
  }

  @Patch('verify/token/:token')
  @HttpCode(HttpStatus.OK)
  @Throttle({ default: { limit: 10, ttl: 60000 } })
  @ApiOperation({
    summary: 'Verify email or phone number by token',
    description:
      'Receives a token and verifies the email address or phone number depending on the type',
  })
  @ApiParam({
    name: 'token',
    description: 'Token generated by the verification service',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Verified successfully',
  })
  @ApiNotFoundResponse({
    description: 'Token not found',
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid or expired token',
  })
  async verifyByToken(@Param('token') token: string) {
    return this.credentialVerificationService.verifyByToken(token);
  }

  @Patch('verify/code/:code')
  @HttpCode(HttpStatus.OK)
  @Throttle({ default: { limit: 5, ttl: 60000 } })
  @ApiOperation({
    summary: 'Verify email or phone number by code',
    description:
      'Receives a code and verifies the email address or phone number depending on the type',
  })
  @ApiParam({
    name: 'code',
    description: 'Code generated by the verification service',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Verified successfully',
  })
  @ApiNotFoundResponse({
    description: 'Code not found',
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid or expired code',
  })
  async verifyByCode(
    @Param('code') code: string,
    @Body() verifyByCodeDto: VerifyByCodeDto,
  ) {
    return this.credentialVerificationService.verifyByCode(
      code,
      verifyByCodeDto.userId,
    );
  }
}
