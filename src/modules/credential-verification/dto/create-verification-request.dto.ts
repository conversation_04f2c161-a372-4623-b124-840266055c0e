import { createInsertSchema } from 'drizzle-zod';
import { createZodDto } from 'nestjs-zod';

import { credentialVerifications } from '@/modules/db/entities/credential-verification.entity';

export const createCredentialVerificationSchema = createInsertSchema(
  credentialVerifications,
).omit({
  expiresAt: true,
});

const autoCreateCredentialVerificationSchema = createInsertSchema(
  credentialVerifications,
).omit({
  id: true,
  expiresAt: true,
  userId: true,
  hashedToken: true,
  code: true,
  createdAt: true,
});

export class CreateCredentialVerificationDto extends createZodDto(
  createCredentialVerificationSchema,
) {}

export class AutoCreateCredentialVerificationDto extends createZodDto(
  autoCreateCredentialVerificationSchema,
) {}
