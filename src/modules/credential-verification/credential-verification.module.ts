import { Module, forwardRef } from '@nestjs/common';

import { CredentialVerificationController } from './credential-verification.controller';
import { CredentialVerificationService } from './credential-verification.service';
import { UsersModule } from '../users/users.module';

@Module({
  imports: [forwardRef(() => UsersModule)],
  controllers: [CredentialVerificationController],
  providers: [CredentialVerificationService],
  exports: [CredentialVerificationService],
})
export class CredentialVerificationModule {}
