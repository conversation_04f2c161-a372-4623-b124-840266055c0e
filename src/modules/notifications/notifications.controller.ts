import { Body, Controller, HttpStatus, Post, UseGuards } from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiForbiddenResponse,
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';

import { Roles } from '@/common/decorators/roles.decorator';
import { Role } from '@/common/enums';
import { RolesGuard } from '@/common/guards/roles.guard';

import { SendNotificationDto } from './dto/send-notification.dto';
import { NotificationsService } from './notifications.service';
import { JwtAuthGuard } from '../auth/guards/jwt.guard';

@ApiTags('Notifications')
@ApiBearerAuth()
@Controller('notifications')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(Role.Partner)
export class NotificationsController {
  constructor(private readonly notificationsService: NotificationsService) {}

  @Post()
  @ApiOperation({
    summary: 'Send notification',
    description: 'Send a push notification to specified users',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Notification sent successfully',
  })
  @ApiBadRequestResponse({ description: 'Invalid notification data' })
  @ApiUnauthorizedResponse({ description: 'Not authenticated' })
  @ApiForbiddenResponse({ description: 'Not authorized to send notifications' })
  sendNotification(@Body() notification: SendNotificationDto) {
    return this.notificationsService.send(notification);
  }
}
