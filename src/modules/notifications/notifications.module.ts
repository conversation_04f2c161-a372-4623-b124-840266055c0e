import { Module, forwardRef } from '@nestjs/common';

import { firebaseAdminProvider } from './firebase-admin.provider';
import { NotificationsController } from './notifications.controller';
import { NotificationsService } from './notifications.service';
import { DevicesModule } from '../devices/devices.module';
import { ManagersModule } from '../managers/managers.module';
import { UsersModule } from '../users/users.module';

@Module({
  imports: [
    DevicesModule,
    forwardRef(() => UsersModule),
    forwardRef(() => ManagersModule),
  ],
  controllers: [NotificationsController],
  providers: [firebaseAdminProvider, NotificationsService],
  exports: [NotificationsService],
})
export class NotificationsModule {}
