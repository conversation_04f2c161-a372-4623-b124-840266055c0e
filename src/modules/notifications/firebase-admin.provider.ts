import { ConfigService } from '@nestjs/config';
import * as admin from 'firebase-admin';

export const firebaseAdminProvider = {
  provide: 'FIREBASE_ADMIN',
  useFactory: (configService: ConfigService) => {
    const defaultApp = admin.initializeApp({
      credential: admin.credential.cert({
        projectId: configService.get('firebase.projectId'),
        clientEmail: configService.get('firebase.clientEmail'),
        privateKey: configService
          .get('firebase.privateKey')
          .replace(/\\n/g, '\n'),
      }),
    });
    return { defaultApp };
  },
  inject: [ConfigService],
};
