import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

export const notificationTopicSchema = z.union([
  z.literal('validate-photo-presence'),
  z.literal('validate-geo-presence'),
  z.literal('validate-complete-presence'),
  z.literal('project-assigned'),
  z.literal('project-unassigned'),
  z.literal('worker-quit'),
  z.literal('worker-terminated'),
  z.literal('registration-request-received'),
  z.literal('registration-request-status-changed'),
  z.literal('manager-removed-from-project'),
  z.literal('manager-assigned-to-project'),
  z.literal('manager-quit'),
  z.literal('manager-terminated'),
]);

export const sendNotificationSchema = z.object({
  title: z.string(),
  body: z.string(),
  receiverUserId: z.string().min(1),
  topic: notificationTopicSchema,
  data: z.object({}).optional(),
});

export class SendNotificationDto extends createZodDto(sendNotificationSchema) {}

export type NotificationTopic = z.infer<typeof notificationTopicSchema>;
