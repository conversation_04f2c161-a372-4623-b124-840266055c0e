import { pgEnum } from 'drizzle-orm/pg-core';

import { COUNTRY_CODES } from '@/common/constants/countries.constant';

export const dayOffReasonEnum = pgEnum('day_off_reason', [
  'sick',
  'vacation',
  'personal',
  'holiday',
  'other',
]);

export const approvalRoleEnum = pgEnum('approval_role', ['worker', 'manager']);
export const codeStatusEnum = pgEnum('code_status', ['active', 'inactive']);

export const approvalStatusEnum = pgEnum('approval_state', [
  'pending',
  'approved',
  'rejected',
]);

export const dayOfWeekEnum = pgEnum('day_of_week', [
  'Monday',
  'Tuesday',
  'Wednesday',
  'Thursday',
  'Friday',
  'Saturday',
  'Sunday',
]);

export const genderEnum = pgEnum('gender', ['male', 'female']);
export const userRoleEnum = pgEnum('user_role', [
  'partner',
  'manager',
  'worker',
]);

export const managerPermissionTypeEnum = pgEnum('manager_permission_type', [
  'all',
  'project_manager',
]);

export const countriesEnum = pgEnum(
  'countries',
  COUNTRY_CODES as [string, ...string[]],
);

export const credentialVerificationTypeEnum = pgEnum('verification_type_enum', [
  'email',
  'phone',
]);

export const reportStatusEnum = pgEnum('report_status', [
  'approved',
  'declined',
  'pending',
  'submitted',
]);

export const devicePlatformEnum = pgEnum('device_platforms', [
  'ios',
  'android',
  'web',
]);

export const employmentChangeEnum = pgEnum('employment_change', [
  'hire',
  'quit',
  'terminated',
  'quit_notice',
  'terminated_notice',
]);

export const securityEnum = pgEnum('security_enum', ['public', 'private']);

export const fileScopeEnum = pgEnum('file_scope', ['user', 'partner']);

export const requestStatusEnum = pgEnum('request_status', [
  'empty',
  'sent',
  'sent_late',
  'confirmed',
  'declined',
]);

export const presenceValidationReasonEnum = pgEnum(
  'presence_validation_reason',
  ['start', 'finish', 'pause', 'unpause', 'check'],
);

export const employmentStatusEnum = pgEnum('employment_status', [
  'active',
  'inactive',
  'quit',
  'terminated',
  'quit_notice',
  'terminated_notice',
]);

export const workingStatusEnum = pgEnum('working_status', [
  'started',
  'finished',
  'paused',
  'passive',
]);

export const validatedPresenceStatusEnum = pgEnum('validated_presence_status', [
  'validated',
  'empty',
  'late',
]);
