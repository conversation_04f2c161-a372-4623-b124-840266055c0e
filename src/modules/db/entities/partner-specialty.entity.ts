import { relations } from 'drizzle-orm';
import { pgTable, primaryKey, varchar } from 'drizzle-orm/pg-core';

import { partners } from './partner.entity';
import { professionCategories } from './profession-category.entity';

export const partnerSpecialties = pgTable(
  'partner_specialty',
  {
    partnerId: varchar('partner_id', { length: 36 })
      .notNull()
      .references(() => partners.id),
    specialtyId: varchar('specialty_id', { length: 36 })
      .notNull()
      .references(() => professionCategories.id),
  },
  (table) => ({
    pk: primaryKey({
      columns: [table.partnerId, table.specialtyId],
    }),
  }),
);

export const partnerSpecialtiesRelations = relations(
  partnerSpecialties,
  ({ one }) => ({
    partner: one(partners, {
      fields: [partnerSpecialties.partnerId],
      references: [partners.id],
    }),
    specialty: one(professionCategories, {
      fields: [partnerSpecialties.specialtyId],
      references: [professionCategories.id],
    }),
  }),
);
