import { relations } from 'drizzle-orm';
import { pgTable, timestamp, varchar } from 'drizzle-orm/pg-core';

import { generateId } from '@/common/utils/generateId';

import { credentialVerificationTypeEnum } from '../enums';
import { users } from './user.entity';

export const credentialVerifications = pgTable('credential_verification', {
  id: varchar('id', { length: 36 })
    .primaryKey()
    .$defaultFn(() => generateId()),
  hashedToken: varchar('hashed_token', { length: 128 }).notNull(),
  code: varchar('code', { length: 6 }).notNull(),
  userId: varchar('user_id', { length: 36 })
    .references(() => users.id)
    .notNull(),
  type: credentialVerificationTypeEnum('type').notNull(),
  contact: varchar('contact', { length: 100 }).notNull(),
  expiresAt: timestamp('expires_at').notNull(),
  createdAt: timestamp('created_at').notNull().defaultNow(),
});

export const credentialVerificationsRelations = relations(
  credentialVerifications,
  ({ one }) => ({
    user: one(users, {
      fields: [credentialVerifications.userId],
      references: [users.id],
    }),
  }),
);
