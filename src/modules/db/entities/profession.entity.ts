import { relations } from 'drizzle-orm';
import { pgTable, text, varchar } from 'drizzle-orm/pg-core';

import { generateId } from '@/common/utils/generateId';

import { professionCategories } from './profession-category.entity';
import { workers } from './worker.entity';

export const professions = pgTable('profession', {
  id: varchar('id', { length: 36 })
    .primaryKey()
    .$defaultFn(() => generateId()),
  categoryId: varchar('category_id', { length: 36 })
    .notNull()
    .references(() => professionCategories.id),
  label: varchar('label', { length: 255 }).notNull(),
  code: varchar('code', { length: 50 }).unique().notNull(),
  description: text('description'),
});

export const professionsRelations = relations(professions, ({ one, many }) => ({
  category: one(professionCategories, {
    fields: [professions.categoryId],
    references: [professionCategories.id],
  }),
  workers: many(workers),
}));
