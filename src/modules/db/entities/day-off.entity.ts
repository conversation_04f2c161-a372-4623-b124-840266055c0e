import { relations } from 'drizzle-orm';
import { date, pgTable, text, timestamp, varchar } from 'drizzle-orm/pg-core';

import { generateId } from '@/common/utils/generateId';

import { approvalStatusEnum, dayOffReasonEnum } from '../enums';
import { users } from './user.entity';
import { workers } from './worker.entity';

export const daysOff = pgTable('day_off', {
  id: varchar('id', { length: 36 })
    .primaryKey()
    .$defaultFn(() => generateId()),
  workerId: varchar('worker_id', { length: 36 })
    .notNull()
    .references(() => workers.id),
  approvedAt: timestamp('approved_at'),
  status: approvalStatusEnum('status').notNull().default('pending'),
  approvedBy: varchar('approved_by', { length: 36 }).references(
    () => users.id,
    { onDelete: 'set null' },
  ),
  offDate: date('off_date').notNull(),
  dayOffReason: dayOffReasonEnum('day_off_reason').notNull(),
  notes: text('notes'),
});

export const daysOffRelations = relations(daysOff, ({ one }) => ({
  worker: one(workers, {
    fields: [daysOff.workerId],
    references: [workers.id],
  }),
  approvedBy: one(users, {
    fields: [daysOff.approvedBy],
    references: [users.id],
  }),
}));
