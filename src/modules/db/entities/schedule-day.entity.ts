import { relations } from 'drizzle-orm';
import { pgTable, time, varchar } from 'drizzle-orm/pg-core';

import { generateId } from '@/common/utils/generateId';

import { schedules } from './schedule.entity';
import { dayOfWeekEnum } from '../enums';
import { workLocations } from './work-location.entity';

export const scheduleDays = pgTable('schedule_day', {
  id: varchar('id', { length: 36 })
    .primaryKey()
    .$defaultFn(() => generateId()),
  scheduleId: varchar('schedule_id', { length: 36 })
    .notNull()
    .references(() => schedules.id),
  dayOfWeek: dayOfWeekEnum('day_of_week').notNull(),
  startTime: time('start_time'),
  endTime: time('end_time'),
  workLocationId: varchar('work_location_id', { length: 36 }).references(
    () => workLocations.id,
  ),
});

export const scheduleDaysRelations = relations(scheduleDays, ({ one }) => ({
  schedule: one(schedules, {
    fields: [scheduleDays.scheduleId],
    references: [schedules.id],
  }),
  workLocation: one(workLocations, {
    fields: [scheduleDays.workLocationId],
    references: [workLocations.id],
  }),
}));
