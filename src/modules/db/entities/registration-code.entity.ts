import { relations } from 'drizzle-orm';
import { boolean, pgTable, timestamp, varchar } from 'drizzle-orm/pg-core';

import { generateId } from '@/common/utils/generateId';

import { managers } from './manager.entity';
import { partners } from './partner.entity';
import { registrationRequests } from './registration-request.entity';
import {
  approvalRoleEnum,
  codeStatusEnum,
  managerPermissionTypeEnum,
} from '../enums';

export const registrationCodes = pgTable('registration_code', {
  id: varchar('id', { length: 36 })
    .primaryKey()
    .$defaultFn(() => generateId()),
  name: varchar('name', { length: 100 }).notNull(),
  profession: varchar('profession', { length: 100 }),
  managerPermissionType: managerPermissionTypeEnum('manager_permission_type'),
  code: varchar('code', { length: 50 }).unique().notNull(),
  managerId: varchar('manager_id', { length: 36 }).references(
    () => managers.id,
  ),
  partnerId: varchar('partner_id', { length: 36 })
    .references(() => partners.id)
    .notNull(),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  expiresAt: timestamp('expires_at'),
  oneTimeCode: boolean('one_time_code').notNull().default(false),
  role: approvalRoleEnum('role').notNull(),
  status: codeStatusEnum('status').notNull().default('active'),
});

export const registrationCodesRelations = relations(
  registrationCodes,
  ({ one, many }) => ({
    manager: one(managers, {
      fields: [registrationCodes.managerId],
      references: [managers.id],
    }),
    partner: one(partners, {
      fields: [registrationCodes.partnerId],
      references: [partners.id],
    }),
    requests: many(registrationRequests),
  }),
);
