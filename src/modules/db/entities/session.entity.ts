import { relations } from 'drizzle-orm';
import {
  boolean,
  pgTable,
  text,
  timestamp,
  varchar,
} from 'drizzle-orm/pg-core';

import { generateId } from '@/common/utils/generateId';

import { devices } from './device.entity';
import { users } from './user.entity';

export const sessions = pgTable('sessions', {
  id: varchar('id', { length: 36 })
    .primaryKey()
    .$defaultFn(() => generateId()),
  userId: varchar('user_id', { length: 36 })
    .references(() => users.id)
    .notNull(),
  deviceId: varchar('device_id', { length: 36 })
    .references(() => devices.id)
    .notNull(),
  hashedRefreshToken: text('hashed_token').notNull(),
  ipAddress: varchar('ip_address', { length: 255 }),
  userAgent: varchar('user_agent', { length: 255 }),
  isActive: boolean('is_active').notNull().default(true),
  lastUsedAt: timestamp('last_used_at').notNull().defaultNow(),
  expiresAt: timestamp('expires_at').notNull(),
  createdAt: timestamp('created_at').notNull().defaultNow(),
});

export const sessionsRelations = relations(sessions, ({ one }) => ({
  user: one(users, {
    fields: [sessions.userId],
    references: [users.id],
  }),
  device: one(devices, {
    fields: [sessions.deviceId],
    references: [devices.id],
  }),
}));
