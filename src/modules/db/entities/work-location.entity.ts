import { relations } from 'drizzle-orm';
import { pgTable, text, varchar } from 'drizzle-orm/pg-core';

import { generateId } from '@/common/utils/generateId';

import { partners } from './partner.entity';
import { scheduleDays } from './schedule-day.entity';

export const workLocations = pgTable('work_location', {
  id: varchar('id', { length: 36 })
    .primaryKey()
    .$defaultFn(() => generateId()),
  name: varchar('name', { length: 255 }).notNull(),
  address: text('address'),
  city: varchar('city', { length: 255 }).notNull(),
  // country: countryEnu('country').notNull(),
  zipCode: varchar('zip_code', { length: 10 }).notNull(),
  partnerId: varchar('partner_id', { length: 36 }).references(
    () => partners.id,
  ),
});

export const workLocationsRelations = relations(
  workLocations,
  ({ one, many }) => ({
    partner: one(partners, {
      fields: [workLocations.partnerId],
      references: [partners.id],
    }),
    scheduleDays: many(scheduleDays),
  }),
);
