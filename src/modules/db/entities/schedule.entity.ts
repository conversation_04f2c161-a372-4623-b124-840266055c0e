import { relations } from 'drizzle-orm';
import {
  boolean,
  pgTable,
  text,
  timestamp,
  varchar,
} from 'drizzle-orm/pg-core';

import { generateId } from '@/common/utils/generateId';

import { scheduleDays } from './schedule-day.entity';
import { workers } from './worker.entity';

export const schedules = pgTable('schedule', {
  id: varchar('id', { length: 36 })
    .primaryKey()
    .$defaultFn(() => generateId()),
  name: varchar('name', { length: 100 }).notNull(),
  description: text('description'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  isActive: boolean('is_active').default(true),
});

export const schedulesRelations = relations(schedules, ({ many }) => ({
  scheduleDays: many(scheduleDays),
  workers: many(workers),
}));
