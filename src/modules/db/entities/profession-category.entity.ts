import { relations } from 'drizzle-orm';
import { pgTable, text, varchar } from 'drizzle-orm/pg-core';

import { generateId } from '@/common/utils/generateId';

import { partnerSpecialties } from './partner-specialty.entity';
import { professions } from './profession.entity';

export const professionCategories = pgTable('profession_category', {
  id: varchar('id', { length: 36 })
    .primaryKey()
    .$defaultFn(() => generateId()),
  label: varchar('label', { length: 255 }).notNull(),
  code: varchar('code', { length: 50 }).unique().notNull(),
  description: text('description'),
});

export const professionCategoriesRelations = relations(
  professionCategories,
  ({ many }) => ({
    professions: many(professions),
    partnerSpecialties: many(partnerSpecialties),
  }),
);
