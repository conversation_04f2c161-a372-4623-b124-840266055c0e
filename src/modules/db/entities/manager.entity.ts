import { relations } from 'drizzle-orm';
import { pgTable, varchar } from 'drizzle-orm/pg-core';

import { generateId } from '@/common/utils/generateId';

import {
  approvalStatusEnum,
  employmentStatusEnum,
  managerPermissionTypeEnum,
} from '../enums';
import { partners } from './partner.entity';
import { projectManagers } from './project-manager.entity';
import { registrationCodes } from './registration-code.entity';
import { registrationRequests } from './registration-request.entity';
import { users } from './user.entity';
import { workers } from './worker.entity';

export const managers = pgTable('manager', {
  id: varchar('id', { length: 36 })
    .primaryKey()
    .$defaultFn(() => generateId()),
  userId: varchar('user_id', { length: 36 })
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
  partnerId: varchar('partner_id', { length: 36 })
    .notNull()
    .references(() => partners.id),
  approvalState: approvalStatusEnum('approval_state').default('pending'),
  permissionType:
    managerPermissionTypeEnum('permission_type').default('project_manager'),
  employmentStatus: employmentStatusEnum('employment_status').default('active'),
  endEmploymentDate: varchar('end_employment_date', { length: 32 }),
});

export const managersRelations = relations(managers, ({ one, many }) => ({
  user: one(users, { fields: [managers.userId], references: [users.id] }),
  partner: one(partners, {
    fields: [managers.partnerId],
    references: [partners.id],
  }),
  workers: many(workers),
  codes: many(registrationCodes),
  registrationRequests: many(registrationRequests),
  projectManagers: many(projectManagers),
}));
