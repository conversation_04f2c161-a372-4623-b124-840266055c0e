import { relations } from 'drizzle-orm';
import { pgTable, timestamp, varchar } from 'drizzle-orm/pg-core';

import { generateId } from '@/common/utils/generateId';

import { employmentChangeEnum } from '../enums';
import { managers } from './manager.entity';
import { partners } from './partner.entity';
import { users } from './user.entity';
import { workers } from './worker.entity';

export const employmentHistory = pgTable('employment_history', {
  id: varchar('id', { length: 36 })
    .primaryKey()
    .$defaultFn(() => generateId()),
  change: employmentChangeEnum('change').notNull(),
  operationAuthorId: varchar('operation_author', { length: 36 })
    .notNull()
    .references(() => users.id, { onDelete: 'set null' }),
  workerId: varchar('worker_id', { length: 36 }).references(() => workers.id),
  managerId: varchar('manager_id', { length: 36 }).references(
    () => managers.id,
  ),
  partnerId: varchar('partner_id', { length: 36 })
    .notNull()
    .references(() => partners.id),
  happenedAt: timestamp('happened_at').notNull().defaultNow(),
});

export const employmentHistoryRelations = relations(
  employmentHistory,
  ({ one }) => ({
    operationAuthor: one(users, {
      fields: [employmentHistory.operationAuthorId],
      references: [users.id],
    }),
    worker: one(workers, {
      fields: [employmentHistory.workerId],
      references: [workers.id],
    }),
    partner: one(partners, {
      fields: [employmentHistory.partnerId],
      references: [partners.id],
    }),
    manager: one(managers, {
      fields: [employmentHistory.managerId],
      references: [managers.id],
    }),
  }),
);
