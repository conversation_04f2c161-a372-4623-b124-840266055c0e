import { relations } from 'drizzle-orm';
import { pgTable, timestamp, varchar } from 'drizzle-orm/pg-core';

import { generateId } from '@/common/utils/generateId';

import { dailyReports } from './daily-report.entity';

export const pauseHistory = pgTable('pause_history', {
  id: varchar('id', { length: 36 })
    .primaryKey()
    .$defaultFn(() => generateId()),
  dailyReportId: varchar('report_id', { length: 36 })
    .references(() => dailyReports.id, {
      onDelete: 'cascade',
    })
    .notNull(),
  pauseStart: timestamp('pause_start').notNull().defaultNow(),
  pauseEnd: timestamp('pause_end'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
});

export const pauseHistoryRelations = relations(pauseHistory, ({ one }) => ({
  report: one(dailyReports, {
    fields: [pauseHistory.dailyReportId],
    references: [dailyReports.id],
  }),
}));
