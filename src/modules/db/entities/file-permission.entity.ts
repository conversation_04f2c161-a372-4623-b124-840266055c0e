import { relations } from 'drizzle-orm';
import { pgTable, primaryKey, timestamp, varchar } from 'drizzle-orm/pg-core';

import { fileScopeEnum } from '../enums';
import { files } from './file.entity';
import { users } from './user.entity';

export const filePermissions = pgTable(
  'file_permission',
  {
    fileId: varchar('file_id', { length: 36 })
      .notNull()
      .references(() => files.id, { onDelete: 'cascade' }),
    userId: varchar('user_id', { length: 36 })
      .notNull()
      .references(() => users.id, { onDelete: 'cascade' }),
    scope: fileScopeEnum('scope').notNull().default('user'),
    createdAt: timestamp('created_at').notNull().defaultNow(),
  },
  (table) => ({
    pk: primaryKey({ columns: [table.fileId, table.userId, table.scope] }),
  }),
);

export const filePermissionsRelations = relations(
  filePermissions,
  ({ one }) => ({
    file: one(files, {
      fields: [filePermissions.fileId],
      references: [files.id],
    }),
    user: one(users, {
      fields: [filePermissions.userId],
      references: [users.id],
    }),
  }),
);
