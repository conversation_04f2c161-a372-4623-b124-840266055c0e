import { relations } from 'drizzle-orm';
import {
  boolean,
  pgTable,
  text,
  timestamp,
  varchar,
} from 'drizzle-orm/pg-core';

import { generateId } from '@/common/utils/generateId';

import { presenceValidationReasonEnum, requestStatusEnum } from '../enums';
import { dailyReports } from './daily-report.entity';
import { files } from './file.entity';
import { users } from './user.entity';

export const presenceValidations = pgTable('presence_validation', {
  id: varchar('id', { length: 36 })
    .primaryKey()
    .$defaultFn(() => generateId()),

  dailyReportId: varchar('daily_report_id', { length: 36 })
    .notNull()
    .references(() => dailyReports.id, { onDelete: 'cascade' }),

  requestedBy: varchar('requested_by', { length: 36 })
    .notNull()
    .references(() => users.id),
  status: requestStatusEnum('status').default('empty'),

  requiresPhoto: boolean('requires_photo').default(false),
  requiresGeo: boolean('requires_geo').default(false),

  photoUrl: text('photo_url'),

  photoId: varchar('photo_id', { length: 36 }).references(() => files.id),
  photoApproved: boolean('photo_approved').default(false),
  geoCoordinates: varchar('geo_coordinates', { length: 255 }),
  geoApproved: boolean('geo_approved').default(false),
  reason: presenceValidationReasonEnum('reason').default('check'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  sentAt: timestamp('sent_at'),
  checkedAt: timestamp('checked_at'),
});

export const presenceValidationsRelations = relations(
  presenceValidations,
  ({ one }) => ({
    dailyReport: one(dailyReports, {
      fields: [presenceValidations.dailyReportId],
      references: [dailyReports.id],
    }),
    requestedBy: one(users, {
      fields: [presenceValidations.requestedBy],
      references: [users.id],
    }),
    file: one(files, {
      fields: [presenceValidations.photoId],
      references: [files.id],
    }),
  }),
);
