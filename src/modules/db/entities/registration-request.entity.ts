import { relations } from 'drizzle-orm';
import { pgTable, timestamp, varchar } from 'drizzle-orm/pg-core';

import { generateId } from '@/common/utils/generateId';

import { managers } from './manager.entity';
import { registrationCodes } from './registration-code.entity';
import { users } from './user.entity';
import { workers } from './worker.entity';
import { approvalStatusEnum } from '../enums';

export const registrationRequests = pgTable('registration_request', {
  id: varchar('id', { length: 36 })
    .primaryKey()
    .$defaultFn(() => generateId()),
  registrationCodeId: varchar('registration_code_id', { length: 36 })
    .notNull()
    .references(() => registrationCodes.id),
  workerId: varchar('worker_id', { length: 36 }).references(() => workers.id, {
    onDelete: 'cascade',
  }),
  managerId: varchar('manager_id', { length: 36 }).references(
    () => managers.id,
  ),
  status: approvalStatusEnum('status').default('pending').notNull(),
  requestedAt: timestamp('requested_at').notNull(),
  approvedBy: varchar('approved_by', { length: 36 }).references(() => users.id),
  approvedAt: timestamp('approved_at'),
  rejectedAt: timestamp('rejected_at'),
});

export const registrationRequestsRelations = relations(
  registrationRequests,
  ({ one }) => ({
    registrationCode: one(registrationCodes, {
      fields: [registrationRequests.registrationCodeId],
      references: [registrationCodes.id],
    }),
    worker: one(workers, {
      fields: [registrationRequests.workerId],
      references: [workers.id],
    }),
    manager: one(managers, {
      fields: [registrationRequests.managerId],
      references: [managers.id],
    }),
    users: one(users, {
      fields: [registrationRequests.approvedBy],
      references: [users.id],
    }),
  }),
);
