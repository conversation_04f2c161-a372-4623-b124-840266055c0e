import { relations } from 'drizzle-orm';
import { boolean, pgTable, timestamp, varchar } from 'drizzle-orm/pg-core';

import { generateId } from '@/common/utils/generateId';

import { devicePlatformEnum } from '../enums';
import { sessions } from './session.entity';
import { users } from './user.entity';

export const devices = pgTable('device', {
  id: varchar('id', { length: 36 })
    .primaryKey()
    .$defaultFn(() => generateId()),
  userId: varchar('user_id', { length: 36 }).references(() => users.id),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  token: varchar('token', { length: 255 }).notNull(),
  platform: devicePlatformEnum('platform').notNull(),
  name: varchar('name', { length: 255 }).notNull(),
  isActive: boolean('is_active').notNull().default(true),
  lastUsedAt: timestamp('last_used_at'),
});

export const devicesRelations = relations(devices, ({ one, many }) => ({
  user: one(users, {
    fields: [devices.userId],
    references: [users.id],
  }),
  sessions: many(sessions),
}));
