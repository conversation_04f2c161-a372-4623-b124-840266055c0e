import { relations } from 'drizzle-orm';
import { pgTable, text, timestamp, varchar } from 'drizzle-orm/pg-core';

import { generateId } from '@/common/utils/generateId';

import { fileScopeEnum, securityEnum } from '../enums';
import { filePermissions } from './file-permission.entity';

export const files = pgTable('file', {
  id: varchar('id', { length: 36 })
    .primaryKey()
    .$defaultFn(() => generateId()),
  s3Key: text('s3_key').notNull(),
  publicFileUrl: text('public_file_url'),
  fileName: text('file_name').notNull(),
  fileType: text('file_type').notNull(),
  security: securityEnum('security').notNull().default('private'),
  scope: fileScopeEnum('scope').notNull().default('user'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  deletedAt: timestamp('deleted_at'),
});

export const filesRelations = relations(files, ({ many }) => ({
  permissions: many(filePermissions),
}));
