import { relations } from 'drizzle-orm';
import { pgTable, primaryKey, timestamp, varchar } from 'drizzle-orm/pg-core';

import { managers } from './manager.entity';
import { projects } from './project.entity';

export const projectManagers = pgTable(
  'project_manager',
  {
    projectId: varchar('project_id', { length: 36 })
      .notNull()
      .references(() => projects.id, { onDelete: 'cascade' }),
    managerId: varchar('manager_id', { length: 36 })
      .notNull()
      .references(() => managers.id, { onDelete: 'cascade' }),
    createdAt: timestamp('created_at').notNull().defaultNow(),
  },
  (table) => ({
    pk: primaryKey({ columns: [table.projectId, table.managerId] }),
  }),
);

export const projectManagersRelations = relations(
  projectManagers,
  ({ one }) => ({
    project: one(projects, {
      fields: [projectManagers.projectId],
      references: [projects.id],
    }),
    manager: one(managers, {
      fields: [projectManagers.managerId],
      references: [managers.id],
    }),
  }),
);
