import { Inject, Injectable, Logger, forwardRef } from '@nestjs/common';
import { Socket } from 'socket.io';

import { Role } from '@/common/enums';
import { ManagerPermissionType } from '@/common/permissions/manager-permissions.config';
import { Forwarded } from '@/common/types';

import { RequestUserType } from '../auth/dto/request-user.dto';
import { ManagersService } from '../managers/managers.service';
import { PartnersService } from '../partners/partners.service';
import { ProjectsService } from '../projects/projects.service';
import { WorkersService } from '../workers/workers.service';

type AuthenticatedSocket = Socket & {
  user: RequestUserType;
};

@Injectable()
export class UpdatesRoomsService {
  private readonly logger = new Logger(UpdatesRoomsService.name);

  constructor(
    @Inject(forwardRef(() => WorkersService))
    private readonly workersService: Forwarded<WorkersService>,
    @Inject(forwardRef(() => PartnersService))
    private readonly partnersService: Forwarded<PartnersService>,
    @Inject(forwardRef(() => ProjectsService))
    private readonly projectsService: Forwarded<ProjectsService>,
    @Inject(forwardRef(() => ManagersService))
    private readonly managersService: Forwarded<ManagersService>,
  ) {}

  async joinUserRooms(socket: AuthenticatedSocket) {
    const user = socket.user;
    let rooms = new Set<string>();
    
    rooms.add(`user:${user.id}`);

    switch (user.role) {
      case Role.Worker:
        await this.joinWorkerRooms(user.entityId, rooms);
        break;
      case Role.Partner:
        await this.joinPartnerRooms(user.entityId, rooms);
        break;
      case Role.Manager:
        await this.joinManagerRooms(user.entityId, rooms);
        break;
    }
    
    socket.join(Array.from(rooms));
    return rooms;
  }

  private async joinWorkerRooms(workerId: string, rooms: Set<string>) {
    const worker = await this.workersService.findOne(workerId);
    if (!worker) {
      this.logger.warn(`Worker not found when joining rooms: ${workerId}`);
      return;
    }

    rooms.add(`worker:${workerId}`);

    if (
      worker.projectId &&
      (worker.employmentStatus === 'active' ||
        worker.employmentStatus === 'quit_notice' ||
        worker.employmentStatus === 'terminated_notice')
    ) {
      rooms.add(`project:${worker.projectId}`);
    }
  }

  private async joinPartnerRooms(partnerId: string, rooms: Set<string>) {
    const partner = await this.partnersService.findOne(partnerId);
    if (!partner) {
      this.logger.warn(`Partner not found when joining rooms: ${partnerId}`);
      return;
    }

    rooms.add(`partner:${partnerId}`);

    const projects = await this.projectsService.findAll({
      id: partner.userId,
      entityId: partner.id,
      role: Role.Partner,
    });
    for (const project of projects) {
      rooms.add(`project:${project.id}`);
    }
    const managers = await this.managersService.findAllByPartner(partner.id);
    for (const manager of managers) {
      rooms.add(`manager:${manager.id}`);
    }
  }

  private async joinManagerRooms(managerId: string, rooms: Set<string>) {
    const manager = await this.managersService.findOne(managerId);
    if (!manager) {
      this.logger.warn(`Manager not found when joining rooms: ${managerId}`);
      return;
    }

    rooms.add(`manager:${managerId}`);

    // Join rooms based on permission type
    if (manager.permissionType === ManagerPermissionType.All) {
      // Managers with "all" permission get similar access to partners
      // Join all projects under the same partner
      const projects = await this.projectsService.findAll({
        id: manager.userId,
        entityId: manager.partnerId,
        role: Role.Partner,
      });
      for (const project of projects) {
        rooms.add(`project:${project.id}`);
      }

      // Join all manager rooms under the same partner (for manager-related events)
      const partnerManagers = await this.managersService.findAllByPartner(manager.partnerId);
      for (const partnerManager of partnerManagers) {
        if (partnerManager.id !== managerId) {
          rooms.add(`manager:${partnerManager.id}`);
        }
      }
    } else if (manager.permissionType === ManagerPermissionType.ProjectManager) {
      // Project managers only join rooms for their assigned projects
      const managerProjects = await this.projectsService.findAll({
        id: manager.userId,
        entityId: manager.id,
        role: Role.Manager,
      });
      for (const project of managerProjects) {
        rooms.add(`project:${project.id}`);
      }
    }
  }
}
