import { Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { Test, TestingModule } from '@nestjs/testing';

import { UpdatesRoomsService } from './updates-rooms.service';
import { UpdatesGateway } from './updates.gateway';
import { UpdatesEvents, UpdatesMessageBody } from './updates.types';
import { ManagersService } from '../managers/managers.service';
import { SessionsService } from '../sessions/sessions.service';
import { UsersService } from '../users/users.service';
import { WorkersService } from '../workers/workers.service';

describe('UpdatesGateway', () => {
  let gateway: UpdatesGateway;
  let mockUpdatesRoomsService: jest.Mocked<UpdatesRoomsService>;
  let mockWorkersService: jest.Mocked<WorkersService>;
  let mockManagersService: jest.Mocked<ManagersService>;
  let mockUsersService: jest.Mocked<UsersService>;
  let mockJwtService: jest.Mocked<JwtService>;
  let mockConfigService: jest.Mocked<ConfigService>;
  let mockSessionsService: jest.Mocked<SessionsService>;

  beforeEach(async () => {
    const mockUpdatesRoomsServiceProvider = {
      provide: UpdatesRoomsService,
      useValue: {
        joinUserRooms: jest.fn(),
      },
    };

    const mockWorkersServiceProvider = {
      provide: WorkersService,
      useValue: {
        findOne: jest.fn(),
      },
    };

    const mockManagersServiceProvider = {
      provide: ManagersService,
      useValue: {
        findOne: jest.fn(),
        findAllByPartner: jest.fn(),
        getManagedProjects: jest.fn(),
      },
    };

    const mockUsersServiceProvider = {
      provide: UsersService,
      useValue: {
        getUserRoleInfo: jest.fn(),
      },
    };

    const mockJwtServiceProvider = {
      provide: JwtService,
      useValue: {
        verifyAsync: jest.fn(),
      },
    };

    const mockConfigServiceProvider = {
      provide: ConfigService,
      useValue: {
        get: jest.fn(),
      },
    };

    const mockSessionsServiceProvider = {
      provide: SessionsService,
      useValue: {
        findActive: jest.fn(),
        updateLastUsed: jest.fn(),
      },
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UpdatesGateway,
        mockUpdatesRoomsServiceProvider,
        mockWorkersServiceProvider,
        mockManagersServiceProvider,
        mockUsersServiceProvider,
        mockJwtServiceProvider,
        mockConfigServiceProvider,
        mockSessionsServiceProvider,
      ],
    }).compile();

    gateway = module.get<UpdatesGateway>(UpdatesGateway);
    mockUpdatesRoomsService = module.get(UpdatesRoomsService);
    mockWorkersService = module.get(WorkersService);
    mockManagersService = module.get(ManagersService);
    mockUsersService = module.get(UsersService);
    mockJwtService = module.get(JwtService);
    mockConfigService = module.get(ConfigService);
    mockSessionsService = module.get(SessionsService);

    // Mock the logger to avoid console output during tests
    jest.spyOn(Logger.prototype, 'log').mockImplementation();
    jest.spyOn(Logger.prototype, 'warn').mockImplementation();
    jest.spyOn(Logger.prototype, 'error').mockImplementation();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(gateway).toBeDefined();
  });

  describe('Event Handler Coverage', () => {
    it('should handle all defined events without throwing errors', async () => {
      // Mock server
      gateway.server = {
        to: jest.fn().mockReturnThis(),
        emit: jest.fn(),
        in: jest.fn().mockReturnThis(),
        socketsJoin: jest.fn(),
        socketsLeave: jest.fn(),
      } as any;

      // Mock worker service response
      mockWorkersService.findOne.mockResolvedValue({
        id: 'worker-1',
        userId: 'user-1',
        projectId: 'project-1',
        partnerId: 'partner-1',
        employmentStatus: 'active',
      } as any);

      // Mock managers service response
      mockManagersService.findAllByPartner.mockResolvedValue([]);
      mockManagersService.findOne.mockResolvedValue(undefined);
      mockManagersService.getManagedProjects.mockResolvedValue([]);

      // Mock user service response
      mockUsersService.getUserRoleInfo.mockResolvedValue({
        type: 'worker',
        entityId: 'worker-1',
      } as any);

      // Test all event types
      const testEvents: Array<{ type: UpdatesEvents; payload: any }> = [
        {
          type: UpdatesEvents.DAILY_REPORT_CREATED,
          payload: { reportId: 'report-1' },
        },
        {
          type: UpdatesEvents.DAILY_REPORT_UPDATED,
          payload: { reportId: 'report-1', status: 'approved' },
        },
        {
          type: UpdatesEvents.DAILY_REPORT_FINISHED,
          payload: { reportId: 'report-1', workerId: 'worker-1' },
        },
        {
          type: UpdatesEvents.PRESENCE_VALIDATION_REQUESTED,
          payload: { validationId: 'validation-1' },
        },
        {
          type: UpdatesEvents.REGISTRATION_REQUEST_RECEIVED,
          payload: { requestId: 'request-1' },
        },
        {
          type: UpdatesEvents.WORKER_STATUS_CHANGED,
          payload: { workerId: 'worker-1', status: 'started' },
        },
        {
          type: UpdatesEvents.PROJECT_UPDATED,
          payload: { projectId: 'project-1', partnerId: 'partner-1' },
        },
        {
          type: UpdatesEvents.PROJECT_CREATED,
          payload: { projectId: 'project-1', partnerId: 'partner-1' },
        },
        {
          type: UpdatesEvents.PROJECT_DELETED,
          payload: { projectId: 'project-1', partnerId: 'partner-1' },
        },
        {
          type: UpdatesEvents.USER_UPDATED,
          payload: { userId: 'user-1' },
        },
        {
          type: UpdatesEvents.WORKER_UPDATED,
          payload: { workerId: 'worker-1' },
        },
        {
          type: UpdatesEvents.PARTNER_UPDATED,
          payload: { partnerId: 'partner-1' },
        },
        {
          type: UpdatesEvents.WORKER_EMPLOYMENT_CHANGED,
          payload: { workerId: 'worker-1', status: 'active' },
        },
        {
          type: UpdatesEvents.WORKER_APPROVAL_CHANGED,
          payload: { workerId: 'worker-1', status: 'approved' },
        },
        {
          type: UpdatesEvents.WORKER_PRESENCE_CHANGED,
          payload: { workerId: 'worker-1', status: 'validated' },
        },
        {
          type: UpdatesEvents.MANAGER_UPDATED,
          payload: { managerId: 'manager-1' },
        },
        {
          type: UpdatesEvents.MANAGER_ASSIGNED_TO_PROJECT,
          payload: { managerId: 'manager-1', projectId: 'project-1' },
        },
        {
          type: UpdatesEvents.MANAGER_REMOVED_FROM_PROJECT,
          payload: { managerId: 'manager-1', projectId: 'project-1' },
        },
        {
          type: UpdatesEvents.MANAGER_EMPLOYMENT_CHANGED,
          payload: { managerId: 'manager-1', status: 'active' },
        },
        {
          type: UpdatesEvents.USER_DELETED,
          payload: { userId: 'user-1' },
        },
        {
          type: UpdatesEvents.PROJECT_MANAGER_UPDATED,
          payload: {
            projectId: 'project-1',
            managerId: 'manager-1',
            action: 'assigned',
          },
        },
      ];

      // Test each event
      for (const event of testEvents) {
        await expect(
          gateway.sendMessage(event as UpdatesMessageBody<any>),
        ).resolves.not.toThrow();
      }
    });

    it('should handle unknown event types gracefully', async () => {
      // Mock server
      gateway.server = {
        to: jest.fn().mockReturnThis(),
        emit: jest.fn(),
      } as any;

      const unknownEvent = {
        type: 'unknown.event' as any,
        payload: { test: 'data' },
      };

      await expect(
        gateway.sendMessage(unknownEvent as UpdatesMessageBody<any>),
      ).resolves.not.toThrow();
    });
  });

  describe('Socket Room Management', () => {
    beforeEach(() => {
      gateway.server = {
        to: jest.fn().mockReturnThis(),
        emit: jest.fn(),
        in: jest.fn().mockReturnThis(),
        socketsJoin: jest.fn(),
        socketsLeave: jest.fn(),
      } as any;
    });

    it('should properly handle project deletion room management', async () => {
      const message = {
        type: UpdatesEvents.PROJECT_DELETED,
        payload: { projectId: 'project-1', partnerId: 'partner-1' },
      };

      await gateway.sendMessage(message);

      expect(gateway.server.socketsLeave).toHaveBeenCalledWith(
        'project:project-1',
      );
    });

    it('should properly handle manager removal from project', async () => {
      const message = {
        type: UpdatesEvents.MANAGER_REMOVED_FROM_PROJECT,
        payload: { managerId: 'manager-1', projectId: 'project-1' },
      };

      await gateway.sendMessage(message);

      expect(gateway.server.in).toHaveBeenCalledWith('manager:manager-1');
      expect(gateway.server.socketsLeave).toHaveBeenCalledWith(
        'project:project-1',
      );
    });
  });

  describe('Manager Notifications', () => {
    beforeEach(() => {
      gateway.server = {
        to: jest.fn().mockReturnThis(),
        emit: jest.fn(),
        in: jest.fn().mockReturnThis(),
        socketsJoin: jest.fn(),
        socketsLeave: jest.fn(),
      } as any;
    });

    it('should notify managers with "all" permission for worker events', async () => {
      // Mock worker details
      mockWorkersService.findOne.mockResolvedValue({
        id: 'worker-1',
        userId: 'user-1',
        projectId: 'project-1',
        partnerId: 'partner-1',
        employmentStatus: 'active',
      } as any);

      // Mock managers - one with "all" permission, one with "project_manager"
      mockManagersService.findAllByPartner.mockResolvedValue([
        {
          id: 'manager-all',
          permissionType: 'all',
          partnerId: 'partner-1',
        },
        {
          id: 'manager-project',
          permissionType: 'project_manager',
          partnerId: 'partner-1',
        },
      ] as any);

      // Mock managed projects for project manager
      mockManagersService.getManagedProjects.mockResolvedValue([
        { id: 'project-1' },
      ] as any);

      const message = {
        type: UpdatesEvents.WORKER_STATUS_CHANGED,
        payload: { workerId: 'worker-1', status: 'started' },
      };

      const rooms = await gateway.sendMessage(message);

      // Should include both managers since project manager manages the worker's project
      expect(gateway.server.to).toHaveBeenCalledWith('manager:manager-all');
      expect(gateway.server.to).toHaveBeenCalledWith('manager:manager-project');
    });

    it('should notify only relevant project managers for project-specific events', async () => {
      // Mock managers
      mockManagersService.findAllByPartner.mockResolvedValue([
        {
          id: 'manager-all',
          permissionType: 'all',
          partnerId: 'partner-1',
        },
        {
          id: 'manager-project-1',
          permissionType: 'project_manager',
          partnerId: 'partner-1',
        },
        {
          id: 'manager-project-2',
          permissionType: 'project_manager',
          partnerId: 'partner-1',
        },
      ] as any);

      // Mock managed projects - only manager-project-1 manages project-1
      mockManagersService.getManagedProjects
        .mockResolvedValueOnce([{ id: 'project-1' }] as any) // for manager-project-1
        .mockResolvedValueOnce([{ id: 'project-2' }] as any); // for manager-project-2

      const message = {
        type: UpdatesEvents.PROJECT_UPDATED,
        payload: { projectId: 'project-1', partnerId: 'partner-1' },
      };

      await gateway.sendMessage(message);

      // Should include manager with "all" permission and the relevant project manager
      expect(gateway.server.to).toHaveBeenCalledWith('manager:manager-all');
      expect(gateway.server.to).toHaveBeenCalledWith('manager:manager-project-1');
      expect(gateway.server.to).not.toHaveBeenCalledWith('manager:manager-project-2');
    });

    it('should notify managers with "all" permission for partner updates', async () => {
      // Mock managers
      mockManagersService.findAllByPartner.mockResolvedValue([
        {
          id: 'manager-all-1',
          permissionType: 'all',
          partnerId: 'partner-1',
        },
        {
          id: 'manager-all-2',
          permissionType: 'all',
          partnerId: 'partner-1',
        },
        {
          id: 'manager-project',
          permissionType: 'project_manager',
          partnerId: 'partner-1',
        },
      ] as any);

      const message = {
        type: UpdatesEvents.PARTNER_UPDATED,
        payload: { partnerId: 'partner-1' },
      };

      await gateway.sendMessage(message);

      // Should only include managers with "all" permission
      expect(gateway.server.to).toHaveBeenCalledWith('manager:manager-all-1');
      expect(gateway.server.to).toHaveBeenCalledWith('manager:manager-all-2');
      expect(gateway.server.to).not.toHaveBeenCalledWith('manager:manager-project');
    });

    it('should notify other managers with "all" permission about manager updates', async () => {
      // Mock the manager being updated
      mockManagersService.findOne.mockResolvedValue({
        id: 'manager-updated',
        permissionType: 'all',
        partnerId: 'partner-1',
      } as any);

      // Mock other managers under the same partner
      mockManagersService.findAllByPartner.mockResolvedValue([
        {
          id: 'manager-updated',
          permissionType: 'all',
          partnerId: 'partner-1',
        },
        {
          id: 'manager-all-other',
          permissionType: 'all',
          partnerId: 'partner-1',
        },
        {
          id: 'manager-project',
          permissionType: 'project_manager',
          partnerId: 'partner-1',
        },
      ] as any);

      const message = {
        type: UpdatesEvents.MANAGER_UPDATED,
        payload: { managerId: 'manager-updated' },
      };

      await gateway.sendMessage(message);

      // Should include the updated manager, partner, and other managers with "all" permission
      expect(gateway.server.to).toHaveBeenCalledWith('manager:manager-updated');
      expect(gateway.server.to).toHaveBeenCalledWith('partner:partner-1');
      expect(gateway.server.to).toHaveBeenCalledWith('manager:manager-all-other');
      expect(gateway.server.to).not.toHaveBeenCalledWith('manager:manager-project');
    });
  });
});
