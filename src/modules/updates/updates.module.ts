import { Module, forwardRef } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';

import { UpdatesRoomsService } from './updates-rooms.service';
import { UpdatesGateway } from './updates.gateway';
import { ManagersModule } from '../managers/managers.module';
import { PartnersModule } from '../partners/partners.module';
import { ProjectsModule } from '../projects/projects.module';
import { SessionsModule } from '../sessions/sessions.module';
import { UsersModule } from '../users/users.module';
import { WorkersModule } from '../workers/workers.module';

@Module({
  imports: [
    forwardRef(() => UsersModule),
    forwardRef(() => WorkersModule),
    forwardRef(() => PartnersModule),
    forwardRef(() => ProjectsModule),
    forwardRef(() => ManagersModule),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get('auth.jwtSecret'),
        signOptions: { expiresIn: '15m' },
      }),
      inject: [ConfigService],
    }),
    SessionsModule,
  ],
  providers: [UpdatesGateway, UpdatesRoomsService],
  exports: [UpdatesGateway],
})
export class UpdatesModule {}
