import { UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { Socket } from 'socket.io';

import { TokenPayloadType } from '../auth/dto/token-payload.dto';
import { JwtStrategy } from '../auth/strategies/jwt.strategy';
import { SessionsService } from '../sessions/sessions.service';
import { UsersService } from '../users/users.service';

type SocketMiddleware = (socket: Socket, next: (err?: Error) => void) => void;

export const WsAuthMiddleware = (
  jwtService: JwtService,
  configService: ConfigService,
  sessionsService: SessionsService,
  userService: UsersService,
): SocketMiddleware => {
  return async (socket: Socket, next) => {
    try {
      const token = socket.handshake?.auth?.token;

      if (!token) {
        throw new Error('Authorization token is missing');
      }

      let payload: TokenPayloadType | null = null;

      try {
        payload = await jwtService.verifyAsync<TokenPayloadType>(token);
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
      } catch (_error: unknown) {
        throw new Error('Authorization token is invalid');
      }

      const strategy = new JwtStrategy(
        configService,
        sessionsService,
        userService,
      );
      const user = await strategy.validate(payload);

      if (!user) {
        throw new Error('User does not exist');
      }

      (socket as any).user = user;
      next();
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (_error: unknown) {
      next(new UnauthorizedException());
    }
  };
};
