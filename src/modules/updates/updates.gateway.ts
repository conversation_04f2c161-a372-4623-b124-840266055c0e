import { Inject, Logger, forwardRef } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import {
  OnGatewayConnection,
  OnGatewayDisconnect,
  WebSocketGateway,
  WebSocketServer,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';

import { Role } from '@/common/enums';
import { ManagerPermissionType } from '@/common/permissions/manager-permissions.config';
import { Forwarded } from '@/common/types';

import { UpdatesRoomsService } from './updates-rooms.service';
import {
  UpdatesEventPayloads,
  UpdatesEvents,
  UpdatesMessageBody,
} from './updates.types';
import { WsAuthMiddleware } from './ws-auth.middleware';
import { RequestUserType } from '../auth/dto/request-user.dto';
import { ManagersService } from '../managers/managers.service';
import { SessionsService } from '../sessions/sessions.service';
import { UsersService } from '../users/users.service';
import { WorkersService } from '../workers/workers.service';

type AuthenticatedSocket = Socket & {
  user: RequestUserType;
};

@WebSocketGateway({
  cors: {
    origin: true,
    credentials: true,
  },
  transports: ['websocket', 'polling'],
  namespace: '/updates',
  pingTimeout: 120000,
  pingInterval: 30000,
})
export class UpdatesGateway
  implements OnGatewayConnection, OnGatewayDisconnect
{
  @WebSocketServer()
  server!: Server;

  private logger: Logger = new Logger('UpdatesGateway');

  constructor(
    private readonly updatesRoomsService: UpdatesRoomsService,
    @Inject(forwardRef(() => WorkersService))
    private readonly workersService: Forwarded<WorkersService>,
    @Inject(forwardRef(() => ManagersService))
    private readonly managersService: Forwarded<ManagersService>,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    private readonly sessionsService: SessionsService,
    @Inject(forwardRef(() => UsersService))
    private readonly usersService: Forwarded<UsersService>,
  ) {}

  async afterInit(server: Server) {
    server.use(
      WsAuthMiddleware(
        this.jwtService,
        this.configService,
        this.sessionsService,
        this.usersService,
      ),
    );
    this.logger.log('WebSocket server initialized');
  }

  async handleConnection(client: Socket) {
    try {
      const authenticatedClient = client as AuthenticatedSocket;

      if (!authenticatedClient.user) {
        throw new Error('Unauthorized connection attempt');
      }

      const token = client.handshake?.auth?.token;
      if (token) {
        try {
          const payload = await this.jwtService.verifyAsync(token);
          const tokenExp = payload.exp * 1000;
          const timeUntilExp = tokenExp - Date.now();

          if (timeUntilExp > 10000) {
            // Disconnect 10 seconds before token expires
            setTimeout(() => {
              this.logger.log(
                `Disconnecting client ${client.id} due to token expiration`,
              );
              client.disconnect(true);
            }, timeUntilExp - 10000);
          }
        } catch (error) {
          this.logger.warn(
            `Could not parse token expiration for client ${client.id}:`,
            error,
          );
        }
      }

      const rooms = await this.updatesRoomsService.joinUserRooms(authenticatedClient);
      this.logger.log(`\n\nClient connected and joined rooms: ${client.id}, ${JSON.stringify(Array.from(rooms))}\n\n`);
      this.logSocketRooms(client, 'after connection');

      // Debug: Listen for join/leave room events (if you use custom events for this)
      client.on('debug-join-room', (room: string) => {
        client.join(room);
        this.logger.log(`Client ${client.id} joined room: ${room}`);
        this.logSocketRooms(client, 'after join-room');
      });
      client.on('debug-leave-room', (room: string) => {
        client.leave(room);
        this.logger.log(`Client ${client.id} left room: ${room}`);
        this.logSocketRooms(client, 'after leave-room');
      });
    } catch (error) {
      this.logger.error(
        `Error handling connection for client ${client.id}:`,
        error,
      );
      client.disconnect();
    }
  }

  handleDisconnect(client: Socket) {
    this.logger.log(`Client disconnected: ${client.id}`);
    this.logSocketRooms(client, 'on disconnect');
  }

  private async delay(ms: number) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private logSocketRooms(client: Socket, context: string) {
    // socket.rooms is a Set in socket.io v4+
    const rooms = Array.from(client.rooms).join(', ');
    this.logger.log(`[${context}] Client ${client.id} is in rooms: ${rooms}`);
  }

  async sendMessage<T extends UpdatesEvents>(message: UpdatesMessageBody<T>) {
    const rooms = await this.determineTargetRooms(message);
    for (const room of rooms) {
      // Debug: log all sockets in the room before emitting
      const socketsInRoom = await this.server.in(room).allSockets();
      this.logger.log(`About to emit to room: ${room}. Sockets in room: ${JSON.stringify(Array.from(socketsInRoom))}`);
      this.server.to(room).emit(message.type, message.payload);
      this.logger.log(`\n\nSent message to room: ${room} for event: ${message.type} with payload: ${JSON.stringify(message.payload)}\n\n`);
      await this.delay(20);
    }
  }

  private async determineTargetRooms<T extends UpdatesEvents>(
    message: UpdatesMessageBody<T>,
  ): Promise<string[]> {
    const rooms = new Set<string>();

    switch (message.type) {
      case UpdatesEvents.USER_UPDATED:
        await this.handleUserUpdate(
          rooms,
          message as UpdatesMessageBody<UpdatesEvents.USER_UPDATED>,
        );
        break;

      case UpdatesEvents.WORKER_UPDATED:
        await this.handleWorkerUpdate(
          rooms,
          message as UpdatesMessageBody<UpdatesEvents.WORKER_UPDATED>,
        );
        break;

      case UpdatesEvents.WORKER_EMPLOYMENT_CHANGED:
      case UpdatesEvents.WORKER_APPROVAL_CHANGED:
      case UpdatesEvents.WORKER_STATUS_CHANGED:
      case UpdatesEvents.WORKER_PRESENCE_CHANGED:
        await this.handleWorkerStatusUpdate(
          rooms,
          message as UpdatesMessageBody<
            | UpdatesEvents.WORKER_EMPLOYMENT_CHANGED
            | UpdatesEvents.WORKER_APPROVAL_CHANGED
            | UpdatesEvents.WORKER_STATUS_CHANGED
            | UpdatesEvents.WORKER_PRESENCE_CHANGED
          >,
        );
        break;

      case UpdatesEvents.PROJECT_CREATED:
        await this.handleProjectCreated(
          rooms,
          message as UpdatesMessageBody<UpdatesEvents.PROJECT_CREATED>,
        );
        break;

      case UpdatesEvents.PROJECT_DELETED:
        await this.handleProjectDeleted(
          rooms,
          message as UpdatesMessageBody<UpdatesEvents.PROJECT_DELETED>,
        );
        break;

      case UpdatesEvents.PROJECT_UPDATED:
        await this.handleProjectUpdated(
          rooms,
          message as UpdatesMessageBody<UpdatesEvents.PROJECT_UPDATED>,
        );
        break;

      case UpdatesEvents.PARTNER_UPDATED:
        await this.handlePartnerUpdated(
          rooms,
          message as UpdatesMessageBody<UpdatesEvents.PARTNER_UPDATED>,
        );
        break;

      case UpdatesEvents.DAILY_REPORT_CREATED:
        await this.handleDailyReportCreated(
          rooms,
          message as UpdatesMessageBody<UpdatesEvents.DAILY_REPORT_CREATED>,
        );
        break;

      case UpdatesEvents.DAILY_REPORT_UPDATED:
        await this.handleDailyReportUpdated(
          rooms,
          message as UpdatesMessageBody<UpdatesEvents.DAILY_REPORT_UPDATED>,
        );
        break;

      case UpdatesEvents.DAILY_REPORT_FINISHED:
        await this.handleDailyReportFinished(
          rooms,
          message as UpdatesMessageBody<UpdatesEvents.DAILY_REPORT_FINISHED>,
        );
        break;

      case UpdatesEvents.PRESENCE_VALIDATION_REQUESTED:
        await this.handlePresenceValidationRequested(
          rooms,
          message as UpdatesMessageBody<UpdatesEvents.PRESENCE_VALIDATION_REQUESTED>,
        );
        break;

      case UpdatesEvents.REGISTRATION_REQUEST_RECEIVED:
        await this.handleRegistrationRequestReceived(
          rooms,
          message as UpdatesMessageBody<UpdatesEvents.REGISTRATION_REQUEST_RECEIVED>,
        );
        break;

      case UpdatesEvents.MANAGER_UPDATED:
        await this.handleManagerUpdated(
          rooms,
          message as UpdatesMessageBody<UpdatesEvents.MANAGER_UPDATED>,
        );
        break;

      case UpdatesEvents.MANAGER_ASSIGNED_TO_PROJECT:
        await this.handleManagerAssignedToProject(
          rooms,
          message as UpdatesMessageBody<UpdatesEvents.MANAGER_ASSIGNED_TO_PROJECT>,
        );
        break;

      case UpdatesEvents.MANAGER_REMOVED_FROM_PROJECT:
        await this.handleManagerRemovedFromProject(
          rooms,
          message as UpdatesMessageBody<UpdatesEvents.MANAGER_REMOVED_FROM_PROJECT>,
        );
        break;

      case UpdatesEvents.USER_DELETED:
        await this.handleUserDeleted(
          rooms,
          message as UpdatesMessageBody<UpdatesEvents.USER_DELETED>,
        );
        break;
      case UpdatesEvents.MANAGER_EMPLOYMENT_CHANGED:
        await this.handleManagerEmploymentChanged(
          rooms,
          message as UpdatesMessageBody<UpdatesEvents.MANAGER_EMPLOYMENT_CHANGED>,
        );
        break;

      case UpdatesEvents.PROJECT_MANAGER_UPDATED:
        await this.handleProjectManagerUpdated(
          rooms,
          message as UpdatesMessageBody<UpdatesEvents.PROJECT_MANAGER_UPDATED>,
        );
        break;

      default:
        this.logger.warn(`Unhandled event type: ${message.type}`);
        break;
    }
    return Array.from(rooms);
  }

  private async handleUserUpdate(
    rooms: Set<string>,
    message: UpdatesMessageBody<UpdatesEvents.USER_UPDATED>,
  ) {
    const userUpdate =
      message.payload as UpdatesEventPayloads[UpdatesEvents.USER_UPDATED];
    const userId = userUpdate.userId;

    rooms.add(`user:${userId}`);

    const userRoleInfo = await this.usersService.getUserRoleInfo(userId);
    if (!userRoleInfo?.entityId) return;

    rooms.add(`${userRoleInfo.type}:${userRoleInfo.entityId}`);

    if (userRoleInfo.type === Role.Worker) {
      await this.addWorkerRelatedRooms(rooms, userRoleInfo.entityId);

      this.emitWorkerInfoChanged(userRoleInfo.entityId, userUpdate.changes);
    }
  }

  private async handleWorkerUpdate(
    rooms: Set<string>,
    message: UpdatesMessageBody<UpdatesEvents.WORKER_UPDATED>,
  ) {
    const workerUpdate =
      message.payload as UpdatesEventPayloads[UpdatesEvents.WORKER_UPDATED];
    const workerDetails = await this.workersService.findOne(
      workerUpdate.workerId,
    );
    if (!workerDetails) return;

    rooms.add(`worker:${workerUpdate.workerId}`);
    rooms.add(`user:${workerDetails.userId}`);

    await this.addWorkerRelatedRooms(rooms, workerUpdate.workerId);
    await this.handleProjectAssignment(rooms, workerUpdate);
  }

  private async handleWorkerStatusUpdate(
    rooms: Set<string>,
    message: UpdatesMessageBody<
      | UpdatesEvents.WORKER_EMPLOYMENT_CHANGED
      | UpdatesEvents.WORKER_APPROVAL_CHANGED
      | UpdatesEvents.WORKER_STATUS_CHANGED
      | UpdatesEvents.WORKER_PRESENCE_CHANGED
    >,
  ) {
    const workerId = (message.payload as { workerId: string }).workerId;
    rooms.add(`worker:${workerId}`);
    await this.addWorkerRelatedRooms(rooms, workerId);
  }

  private async handleProjectCreated(
    rooms: Set<string>,
    message: UpdatesMessageBody<UpdatesEvents.PROJECT_CREATED>,
  ) {
    const { projectId, partnerId } =
      message.payload as UpdatesEventPayloads[UpdatesEvents.PROJECT_CREATED];
    rooms.add(`project:${projectId}`);
    rooms.add(`partner:${partnerId}`);

    // Add manager notifications based on permission types
    await this.addManagerRelatedRooms(rooms, partnerId, projectId);

    this.server.in(`partner:${partnerId}`).socketsJoin(`project:${projectId}`);

    // Also join managers with "all" permission to the new project room
    const managers = await this.managersService.findAllByPartner(partnerId);
    for (const manager of managers) {
      if (manager.permissionType === ManagerPermissionType.All) {
        this.server.in(`manager:${manager.id}`).socketsJoin(`project:${projectId}`);
      }
    }
  }

  private async handleProjectDeleted(
    rooms: Set<string>,
    message: UpdatesMessageBody<UpdatesEvents.PROJECT_DELETED>,
  ) {
    const { projectId, partnerId } =
      message.payload as UpdatesEventPayloads[UpdatesEvents.PROJECT_DELETED];
    rooms.add(`project:${projectId}`);
    rooms.add(`partner:${partnerId}`);

    // Add manager notifications based on permission types
    await this.addManagerRelatedRooms(rooms, partnerId, projectId);

    this.server.in(`project:${projectId}`).socketsLeave(`project:${projectId}`);
  }

  private async handleProjectUpdated(
    rooms: Set<string>,
    message: UpdatesMessageBody<UpdatesEvents.PROJECT_UPDATED>,
  ) {
    const { projectId, partnerId } =
      message.payload as UpdatesEventPayloads[UpdatesEvents.PROJECT_UPDATED];
    rooms.add(`project:${projectId}`);
    if (partnerId) {
      rooms.add(`partner:${partnerId}`);
      // Add manager notifications based on permission types
      await this.addManagerRelatedRooms(rooms, partnerId, projectId);
    }
  }

  private async handlePartnerUpdated(
    rooms: Set<string>,
    message: UpdatesMessageBody<UpdatesEvents.PARTNER_UPDATED>,
  ) {
    const { partnerId } =
      message.payload as UpdatesEventPayloads[UpdatesEvents.PARTNER_UPDATED];
    rooms.add(`partner:${partnerId}`);

    // Add manager notifications - managers with "all" permission should know about partner updates
    const managers = await this.managersService.findAllByPartner(partnerId);
    for (const manager of managers) {
      if (manager.permissionType === ManagerPermissionType.All) {
        rooms.add(`manager:${manager.id}`);
      }
    }
  }

  private async addWorkerRelatedRooms(rooms: Set<string>, workerId: string) {
    const workerDetails = await this.workersService.findOne(workerId);
    if (!workerDetails) return;

    if (workerDetails.projectId) {
      rooms.add(`project:${workerDetails.projectId}`);
    }
    if (workerDetails.partnerId) {
      rooms.add(`partner:${workerDetails.partnerId}`);
      // Add manager notifications based on permission types
      await this.addManagerRelatedRooms(
        rooms,
        workerDetails.partnerId,
        workerDetails.projectId || undefined,
        workerId
      );
    }
  }

  private async addPartnerRelatedRooms(rooms: Set<string>, partnerId: string) {
    const managers = await this.managersService.findAllByPartner(partnerId);
    for (const manager of managers) {
      rooms.add(`manager:${manager.id}`);
    }
  }

  private async addManagerRelatedRooms(rooms: Set<string>, partnerId: string, projectId?: string, workerId?: string) {
    const managers = await this.managersService.findAllByPartner(partnerId);

    for (const manager of managers) {
      if (manager.permissionType === ManagerPermissionType.All) {
        // Managers with "all" permission get notifications for everything under their partner
        rooms.add(`manager:${manager.id}`);
      } else if (manager.permissionType === ManagerPermissionType.ProjectManager && projectId) {
        // Project managers only get notifications for their assigned projects
        const managedProjects = await this.managersService.getManagedProjects(manager.id);
        const isProjectManager = managedProjects.some(project => project.id === projectId);

        if (isProjectManager) {
          rooms.add(`manager:${manager.id}`);
        }
      } else if (manager.permissionType === ManagerPermissionType.ProjectManager && workerId && !projectId) {
        // For worker-related events without explicit projectId, check if worker is in manager's projects
        const workerDetails = await this.workersService.findOne(workerId);
        if (workerDetails?.projectId) {
          const managedProjects = await this.managersService.getManagedProjects(manager.id);
          const isProjectManager = managedProjects.some(project => project.id === workerDetails.projectId);

          if (isProjectManager) {
            rooms.add(`manager:${manager.id}`);
          }
        }
      }
    }
  }

  private async handleProjectAssignment(
    rooms: Set<string>,
    workerUpdate: UpdatesEventPayloads[UpdatesEvents.WORKER_UPDATED],
  ) {
    if (!workerUpdate.projectId) return;

    rooms.add(`project:${workerUpdate.projectId}`);

    if (workerUpdate.action === 'project_assigned') {
      this.server
        .in(`worker:${workerUpdate.workerId}`)
        .socketsJoin(`project:${workerUpdate.projectId}`);
    } else if (workerUpdate.action === 'project_unassigned') {
      this.server
        .in(`worker:${workerUpdate.workerId}`)
        .socketsLeave(`project:${workerUpdate.projectId}`);
    }
  }

  private async handleDailyReportCreated(
    rooms: Set<string>,
    message: UpdatesMessageBody<UpdatesEvents.DAILY_REPORT_CREATED>,
  ) {
    const { reportId, workerId, projectId } =
      message.payload as UpdatesEventPayloads[UpdatesEvents.DAILY_REPORT_CREATED];

    rooms.add(`worker:${workerId}`);
    rooms.add(`project:${projectId}`);

    await this.addWorkerRelatedRooms(rooms, workerId);

    this.logger.log(`Daily report created: ${reportId} for worker: ${workerId} in project: ${projectId}`);
  }

  private async handleDailyReportUpdated(
    rooms: Set<string>,
    message: UpdatesMessageBody<UpdatesEvents.DAILY_REPORT_UPDATED>,
  ) {
    const { reportId, workerId, projectId, status } =
      message.payload as UpdatesEventPayloads[UpdatesEvents.DAILY_REPORT_UPDATED];

    rooms.add(`worker:${workerId}`);
    rooms.add(`project:${projectId}`);

    await this.addWorkerRelatedRooms(rooms, workerId);

    this.logger.log(`Daily report updated: ${reportId} for worker: ${workerId} in project: ${projectId}, status: ${status}`);
  }

  private async handleDailyReportFinished(
    rooms: Set<string>,
    message: UpdatesMessageBody<UpdatesEvents.DAILY_REPORT_FINISHED>,
  ) {
    const { workerId } =
      message.payload as UpdatesEventPayloads[UpdatesEvents.DAILY_REPORT_FINISHED];
    rooms.add(`worker:${workerId}`);
    await this.addWorkerRelatedRooms(rooms, workerId);
  }

  private async handlePresenceValidationRequested(
    rooms: Set<string>,
    message: UpdatesMessageBody<UpdatesEvents.PRESENCE_VALIDATION_REQUESTED>,
  ) {
    const { validationId, workerId, projectId } =
      message.payload as UpdatesEventPayloads[UpdatesEvents.PRESENCE_VALIDATION_REQUESTED];

    rooms.add(`worker:${workerId}`);
    rooms.add(`project:${projectId}`);

    await this.addWorkerRelatedRooms(rooms, workerId);

    this.logger.log(`Presence validation requested: ${validationId} for worker: ${workerId} in project: ${projectId}`);
  }

  private async handleRegistrationRequestReceived(
    rooms: Set<string>,
    message: UpdatesMessageBody<UpdatesEvents.REGISTRATION_REQUEST_RECEIVED>,
  ) {
    const { requestId, partnerId } =
      message.payload as UpdatesEventPayloads[UpdatesEvents.REGISTRATION_REQUEST_RECEIVED];

    rooms.add(`partner:${partnerId}`);
    await this.addPartnerRelatedRooms(rooms, partnerId);

    this.logger.log(`Registration request received: ${requestId} for partner: ${partnerId}`);
  }

  private emitWorkerInfoChanged(workerId: string, changes: any) {
    const workerUpdatePayload: UpdatesEventPayloads[UpdatesEvents.WORKER_UPDATED] =
      {
        workerId,
        action: 'user_info_changed',
        changes,
      };

    this.server
      .to(`worker:${workerId}`)
      .emit(UpdatesEvents.WORKER_UPDATED, workerUpdatePayload);
  }

  private async handleManagerUpdated(
    rooms: Set<string>,
    message: UpdatesMessageBody<UpdatesEvents.MANAGER_UPDATED>,
  ) {
    const { managerId } = message.payload;
    rooms.add(`manager:${managerId}`);

    // Also notify the partner and other managers with "all" permission
    const manager = await this.managersService.findOne(managerId);
    if (manager) {
      rooms.add(`partner:${manager.partnerId}`);

      // Notify other managers with "all" permission about manager updates
      const partnerManagers = await this.managersService.findAllByPartner(manager.partnerId);
      for (const partnerManager of partnerManagers) {
        if (partnerManager.permissionType === ManagerPermissionType.All && partnerManager.id !== managerId) {
          rooms.add(`manager:${partnerManager.id}`);
        }
      }
    }
  }

  private async handleManagerAssignedToProject(
    rooms: Set<string>,
    message: UpdatesMessageBody<UpdatesEvents.MANAGER_ASSIGNED_TO_PROJECT>,
  ) {
    const { managerId, projectId } = message.payload;
    rooms.add(`manager:${managerId}`);
    rooms.add(`project:${projectId}`);

    // Also notify the partner and other managers with "all" permission
    const manager = await this.managersService.findOne(managerId);
    if (manager) {
      rooms.add(`partner:${manager.partnerId}`);
      await this.addManagerRelatedRooms(rooms, manager.partnerId, projectId);
    }
  }

  private async handleManagerRemovedFromProject(
    rooms: Set<string>,
    message: UpdatesMessageBody<UpdatesEvents.MANAGER_REMOVED_FROM_PROJECT>,
  ) {
    const { managerId, projectId } = message.payload;
    rooms.add(`manager:${managerId}`);
    rooms.add(`project:${projectId}`);

    // Also notify the partner and other managers with "all" permission
    const manager = await this.managersService.findOne(managerId);
    if (manager) {
      rooms.add(`partner:${manager.partnerId}`);
      await this.addManagerRelatedRooms(rooms, manager.partnerId, projectId);
    }

    this.server.in(`manager:${managerId}`).socketsLeave(`project:${projectId}`);
    this.server.in(`project:${projectId}`).socketsLeave(`manager:${managerId}`);
  }

  private async handleManagerEmploymentChanged(
    rooms: Set<string>,
    message: UpdatesMessageBody<UpdatesEvents.MANAGER_EMPLOYMENT_CHANGED>,
  ) {
    const { managerId } = message.payload;
    rooms.add(`manager:${managerId}`);

    // Also notify the partner and other managers with "all" permission
    const manager = await this.managersService.findOne(managerId);
    if (manager) {
      rooms.add(`partner:${manager.partnerId}`);

      // Notify other managers with "all" permission about employment changes
      const partnerManagers = await this.managersService.findAllByPartner(manager.partnerId);
      for (const partnerManager of partnerManagers) {
        if (partnerManager.permissionType === ManagerPermissionType.All && partnerManager.id !== managerId) {
          rooms.add(`manager:${partnerManager.id}`);
        }
      }
    }
  }

  private async handleUserDeleted(
    rooms: Set<string>,
    message: UpdatesMessageBody<UpdatesEvents.USER_DELETED>,
  ) {
    const { userId } = message.payload;
    rooms.add(`user:${userId}`);
    this.server.in(`user:${userId}`).socketsLeave(`user:${userId}`);
  }

  private async handleProjectManagerUpdated(
    rooms: Set<string>,
    message: UpdatesMessageBody<UpdatesEvents.PROJECT_MANAGER_UPDATED>,
  ) {
    const { projectId, managerId, action } = message.payload;
    rooms.add(`project:${projectId}`);
    rooms.add(`manager:${managerId}`);

    // Also notify the partner and other managers with "all" permission
    const manager = await this.managersService.findOne(managerId);
    if (manager) {
      rooms.add(`partner:${manager.partnerId}`);
      await this.addManagerRelatedRooms(rooms, manager.partnerId, projectId);
    }

    if (action === 'assigned') {
      this.server
        .in(`project:${projectId}`)
        .socketsJoin(`manager:${managerId}`);
    } else if (action === 'removed') {
      this.server
        .in(`project:${projectId}`)
        .socketsLeave(`manager:${managerId}`);
    }
  }
}
