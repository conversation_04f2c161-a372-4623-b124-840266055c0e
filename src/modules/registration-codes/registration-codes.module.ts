import { Module, forwardRef } from '@nestjs/common';

import { Registration<PERSON>odes<PERSON>ontroller } from './registration-codes.controller';
import { RegistrationCodesService } from './registration-codes.service';
import { ManagersModule } from '../managers/managers.module';
import { RegistrationRequestsModule } from '../registration-requests/registration-requests.module';
import { UsersModule } from '../users/users.module';
import { WorkersModule } from '../workers/workers.module';

@Module({
  imports: [
    forwardRef(() => UsersModule),
    forwardRef(() => WorkersModule),
    forwardRef(() => ManagersModule),
    forwardRef(() => RegistrationRequestsModule),
  ],
  exports: [RegistrationCodesService],
  providers: [RegistrationCodesService],
  controllers: [RegistrationCodesController],
})
export class RegistrationCodesModule {}
