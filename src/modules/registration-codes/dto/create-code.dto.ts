import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

export const createCodeSchema = z.object({
  expiresAt: z.preprocess(
    (val) => (typeof val === 'string' ? new Date(val) : val),
    z.date(),
  ),
  oneTimeCode: z.boolean().optional(),
  role: z.enum(['manager', 'worker']),
  profession: z.string().optional(),
  managerPermissionType: z.enum(['all', 'project_manager']).optional(),
  name: z.string().min(2).max(100),
});

export class CreateCodeDto extends createZodDto(createCodeSchema) {}
