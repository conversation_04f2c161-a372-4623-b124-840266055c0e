import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

export const updateCodeSchema = z
  .object({
    expiresAt: z.preprocess(
      (val) => (typeof val === 'string' ? new Date(val) : val),
      z.date(),
    ),
    oneTimeCode: z.boolean().optional(),
    role: z.enum(['manager', 'worker']),
    profession: z.string().optional(),
    name: z.string().min(2).max(100),
  })
  .partial();

export class UpdateCodeDto extends createZodDto(updateCodeSchema) {}
