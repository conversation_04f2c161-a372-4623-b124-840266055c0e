import { RedisService } from '@liaoliaots/nestjs-redis';
import { Injectable } from '@nestjs/common';
import Redis from 'ioredis';

import { generateId } from '@/common/utils/generateId';

import { CreateSessionDto } from './dto/create-session.dto';

type SessionData = {
  id: string;
  userId: string;
  deviceId: string;
  userRole: 'worker' | 'partner' | 'manager';
  hashedRefreshToken: string;
  ipAddress?: string;
  userAgent?: string;
  lastUsedAt: string;
};

@Injectable()
export class SessionsService {
  private readonly redis: Redis;

  constructor(private readonly redisService: RedisService) {
    this.redis = this.redisService.getOrThrow();
  }

  private getRedisKey(userId: string, deviceId: string): string {
    return `session:${userId}:${deviceId}`;
  }

  private async getSessionFromRedis(
    sessionId: string,
  ): Promise<SessionData | null> {
    const sessionData = await this.redis.hgetall(`session:${sessionId}`);

    if (!sessionData || Object.keys(sessionData).length === 0) {
      return null;
    }
    return sessionData as SessionData;
  }

  async create(createSessionDto: CreateSessionDto) {
    const compositeKey = this.getRedisKey(
      createSessionDto.userId,
      createSessionDto.deviceId,
    );
    const sessionId = createSessionDto.id || generateId();

    const expiresIn = 60 * 60 * 24 * 30;
    await this.redis.set(compositeKey, sessionId, 'EX', expiresIn);

    const sessionData: SessionData = {
      id: sessionId,
      userId: createSessionDto.userId,
      userRole: createSessionDto.userRole,
      deviceId: createSessionDto.deviceId,
      hashedRefreshToken: createSessionDto.hashedRefreshToken,
      ipAddress: createSessionDto.ipAddress,
      userAgent: createSessionDto.userAgent,
      lastUsedAt: new Date().toISOString(),
    };

    await this.redis.hmset(`session:${sessionId}`, sessionData);
    await this.redis.expire(`session:${sessionId}`, expiresIn);

    return { id: sessionId };
  }

  async deactivate(id: string) {
    return this.redis.del(`session:${id}`);
  }

  async deactivateForDevice(deviceId: string, userId: string) {
    return this.redis.del(`session:${userId}:${deviceId}`);
  }

  async deactivateAllUserSessions(userId: string, exceptDeviceId?: string) {
    const keys = await this.redis.keys(`session:${userId}:*`);

    if (exceptDeviceId) {
      keys.filter((key) => key !== `session:${userId}:${exceptDeviceId}`);
    }

    return this.redis.del(...keys);
  }

  async findActiveByDevice(
    userId: string,
    deviceId: string,
  ): Promise<SessionData | null> {
    const compositeKey = this.getRedisKey(userId, deviceId);
    const sessionId = await this.redis.get(compositeKey);

    if (!sessionId) {
      return null;
    }

    const sessionData = await this.getSessionFromRedis(sessionId);
    return sessionData;
  }

  async findActive(sessionId: string) {
    const sessionData = await this.getSessionFromRedis(sessionId);
    return sessionData;
  }

  async updateLastUsed(id: string) {
    const now = new Date().toISOString();

    await this.redis.hset(`session:${id}`, 'lastUsedAt', now);
  }
}
