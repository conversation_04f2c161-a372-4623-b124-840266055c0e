import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

export const createSessionSchema = z.object({
  id: z.string().optional(),
  userId: z.string(),
  deviceId: z.string(),
  userRole: z.enum(['partner', 'worker', 'manager']),
  hashedRefreshToken: z.string(),
  ipAddress: z.string().optional(),
  userAgent: z.string().optional(),
});

export class CreateSessionDto extends createZodDto(createSessionSchema) {}
