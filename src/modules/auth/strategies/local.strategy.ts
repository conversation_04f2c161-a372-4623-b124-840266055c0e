import {
  BadRequestException,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Request } from 'express';
import { Strategy } from 'passport-local';

import { DevicesService } from '@/modules/devices/devices.service';
import { UsersService } from '@/modules/users/users.service';

import { AuthService } from '../auth.service';
import { LocalStrategyReturnType } from '../dto/request-user.dto';

@Injectable()
export class LocalStrategy extends PassportStrategy(Strategy) {
  constructor(
    private readonly authService: AuthService,
    private readonly usersService: UsersService,
    private readonly devicesService: DevicesService,
  ) {
    super({
      usernameField: 'credential',
      passwordField: 'password',
      passReqToCallback: true,
    });
  }

  async validate(
    request: Request,
    credential: string,
    password: string,
  ): Promise<LocalStrategyReturnType> {
    const user = await this.authService.validateUser({ credential, password });
    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    const { deviceId } = request.body;
    let device;

    if (deviceId) {
      device = await this.devicesService.findOne(deviceId);
    } else {
      throw new BadRequestException(
        'Device ID is required for login. Please register device first',
      );
    }

    if (!device) {
      throw new BadRequestException(
        'Device not registered. Please register device first',
      );
    }

    device = await this.devicesService.claimDevice(device.id, user.id);

    const userRoleInfo = await this.usersService.getUserRoleInfo(user.id);
    return {
      id: user.id,
      deviceId: device.id,
      role: user.role,
      entityId: userRoleInfo.entityId,
    };
  }
}
