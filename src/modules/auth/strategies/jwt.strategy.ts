import {
  HttpException,
  Inject,
  Injectable,
  UnauthorizedException,
  forwardRef,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';

import { Forwarded } from '@/common/types';
import { SessionsService } from '@/modules/sessions/sessions.service';
import { UsersService } from '@/modules/users/users.service';

import { RequestUserWithNullableEntityIdType } from '../dto/request-user.dto';
import { TokenPayloadDto } from '../dto/token-payload.dto';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy, 'jwt') {
  constructor(
    configService: ConfigService,
    private readonly sessionsService: SessionsService,
    @Inject(forwardRef(() => UsersService))
    private readonly usersService: Forwarded<UsersService>,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: configService.get('auth.jwtSecret')!,
    });
  }

  async validate(
    payload: TokenPayloadDto,
  ): Promise<RequestUserWithNullableEntityIdType> {
    try {
      const session = await this.sessionsService.findActive(payload.sessionId);
      if (!session) {
        throw new UnauthorizedException();
      }
      await this.sessionsService.updateLastUsed(session.id);
      const entityId = await this.usersService.getEntityId(payload.id);

      return {
        id: payload.id,
        sessionId: payload.sessionId,
        role: session.userRole,
        entityId,
      };
    } catch (error) {
      if (!(error instanceof HttpException)) {
        console.error('Error validating JWT:', error);
      }
      throw new UnauthorizedException();
    }
  }
}
