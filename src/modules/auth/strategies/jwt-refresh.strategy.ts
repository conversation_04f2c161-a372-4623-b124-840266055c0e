import {
  BadRequestException,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PassportStrategy } from '@nestjs/passport';
import { Request } from 'express';
import { ExtractJwt, Strategy } from 'passport-jwt';

import { SessionsService } from '@/modules/sessions/sessions.service';
import { UsersService } from '@/modules/users/users.service';

import { AuthService } from '../auth.service';
import { LocalStrategyReturnType } from '../dto/request-user.dto';
import { TokenPayloadDto } from '../dto/token-payload.dto';

@Injectable()
export class JwtRefreshStrategy extends PassportStrategy(
  Strategy,
  'jwt-refresh',
) {
  constructor(
    configService: ConfigService,
    private readonly authService: AuthService,
    private readonly usersService: UsersService,
    private readonly sessionsService: SessionsService,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromBodyField('refresh'),
      ignoreExpiration: false,
      secretOrKey: configService.get('auth.jwtRefreshTokenSecret')!,
      passReqToCallback: true,
    });
  }

  async validate(
    req: Request,
    payload: TokenPayloadDto,
  ): Promise<LocalStrategyReturnType> {
    const refreshToken = req.body?.refresh;
    const sessionId = payload.sessionId;
    if (!refreshToken || !sessionId) {
      throw new BadRequestException('Missing refresh token or session id');
    }

    const session = await this.sessionsService.findActive(sessionId);

    if (!session) {
      throw new UnauthorizedException();
    }

    const user = await this.authService.validateUserRefreshToken(
      refreshToken,
      sessionId,
    );

    if (!user) {
      throw new UnauthorizedException();
    }

    const entityId = await this.usersService.getEntityId(user.id);

    return {
      id: payload.id,
      deviceId: session.deviceId,
      role: user.role,
      entityId,
    };
  }
}
