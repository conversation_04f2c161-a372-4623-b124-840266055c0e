import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

export const loginSchema = z.object({
  credential: z
    .string()
    .email()
    .or(
      z
        .string()
        .startsWith('+', { message: 'Phone number must start with a + sign' })
        .refine((phoneNumber) => !phoneNumber.includes('@'), {
          message: 'Phone number must not contain an @ sign',
        }),
    ),
  password: z.string(),
  deviceId: z.string(),
});

export class LoginDto extends createZodDto(loginSchema) {}
