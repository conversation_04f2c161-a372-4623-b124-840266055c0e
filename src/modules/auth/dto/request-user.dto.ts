import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

export const roleInfoSchema = z.union([
  z.object({
    type: z.literal('partner'),
    entityId: z.string(),
  }),
  z.object({
    type: z.literal('manager'),
    entityId: z.string(),
    approvalState: z.enum(['pending', 'approved', 'rejected']).nullable(),
    permissionType: z.enum(['all', 'project_manager']).nullable(),
    hasProjects: z.boolean(),
    employmentStatus: z
      .enum([
        'active',
        'inactive',
        'quit',
        'terminated',
        'quit_notice',
        'terminated_notice',
      ])
      .nullable(),
  }),
  z.object({
    type: z.literal('worker'),
    entityId: z.string().nullable(),
    approvalState: z.enum(['pending', 'approved', 'rejected']).nullable(),
    independent: z.boolean(),
    employmentStatus: z
      .enum([
        'active',
        'inactive',
        'quit',
        'terminated',
        'quit_notice',
        'terminated_notice',
      ])
      .nullable(),
  }),
]);

export const requestUserSchema = z.object({
  id: z.string(),
  sessionId: z.string(),
  role: z.enum(['partner', 'worker', 'manager']),
  entityId: z.string().nullable(),
});

export class RequestUserDto extends createZodDto(requestUserSchema) {}

export const requestUserWithEntityIdSchema = requestUserSchema.extend({
  entityId: z.string(),
});

export const localStrategyReturnSchema = requestUserSchema
  .omit({
    sessionId: true,
  })
  .extend({
    deviceId: z.string(),
  });

export type RequestUserWithNullableEntityIdType = z.infer<
  typeof requestUserSchema
>;

export type LocalStrategyReturnType = z.infer<typeof localStrategyReturnSchema>;

export type RequestUserType = z.infer<typeof requestUserWithEntityIdSchema>;

export { RequestUserWithNullableEntityIdType as RequestWorkerUserType };
