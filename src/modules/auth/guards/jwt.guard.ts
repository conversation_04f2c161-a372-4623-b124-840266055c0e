import {
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { AuthGuard } from '@nestjs/passport';

import { ALLOW_UNVERIFIED_EMAIL_KEY } from '@/common/decorators/allow-unverified-email.decorator';
import { UsersService } from '@/modules/users/users.service';

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  constructor(
    private reflector: Reflector,
    private readonly usersService: UsersService,
  ) {
    super();
  }

  async canActivate(context: ExecutionContext) {
    const isJwtValid = await super.canActivate(context);

    if (!isJwtValid) {
      return false;
    }

    const allowUnverifiedEmail = this.reflector.getAllAndOverride<boolean>(
      ALLOW_UNVERIFIED_EMAIL_KEY,
      [context.getHandler(), context.getClass()],
    );

    if (allowUnverifiedEmail) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const { user } = request;

    if (!user || !user.id || user.deletedAt) {
      throw new UnauthorizedException('Authentication required');
    }

    const metadata = await this.usersService.getUserMetaData(user.id);
    if (!metadata) {
      throw new UnauthorizedException('User not found');
    }

    if (!metadata.verifications.isEmailVerified) {
      throw new UnauthorizedException('Email verification required');
    }

    return true;
  }
}
