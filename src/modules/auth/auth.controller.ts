import {
  Body,
  Controller,
  HttpCode,
  HttpStatus,
  Post,
  Req,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiConflictResponse,
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import { Throttle } from '@nestjs/throttler';
import { MailerService } from '@nestjs-modules/mailer';
import { Request } from 'express';

import { AllowUnverifiedEmail } from '@/common/decorators/allow-unverified-email.decorator';
import { User } from '@/common/decorators/user.decorator';
import { passwordResetEmailTemplate } from '@/common/emails/password-reset.email';
import { Role } from '@/common/enums';

import { AuthService } from './auth.service';
import { GeneratePasswordResetTokenDto } from './dto/generate-password-reset-token.dto';
import { LoginResponseDto } from './dto/login-response.dto';
import { LoginDto } from './dto/login.dto';
import { PasswordResetDto } from './dto/password-reset.dto';
import { RefreshTokenDto } from './dto/refresh-token.dto';
import { ManagerRegisterDto } from './dto/register-manager.dto';
import { PartnerRegisterDto } from './dto/register-partner.dto';
import { RegisterResponseDto } from './dto/register-response.dto';
import { WorkerRegisterDto } from './dto/register-worker.dto';
import {
  LocalStrategyReturnType,
  RequestUserType,
} from './dto/request-user.dto';
import { JwtRefreshAuthGuard } from './guards/jwt-refresh.guard';
import { JwtAuthGuard } from './guards/jwt.guard';
import { LocalAuthGuard } from './guards/local.guard';

@ApiTags('Authentication')
@Controller('auth')
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly mailerService: MailerService,
  ) {}

  @Post('login')
  @HttpCode(HttpStatus.OK)
  @UseGuards(LocalAuthGuard)
  @Throttle({ default: { limit: 3, ttl: 1000 } })
  @ApiOperation({
    summary: 'Login',
    description: 'Login with email and password',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Successfully authenticated',
    type: LoginResponseDto,
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid credentials',
  })
  login(
    @User() user: LocalStrategyReturnType,
    @Body() _loginDto: LoginDto,
    @Req() request: Request,
  ): Promise<LoginResponseDto> {
    return this.authService.login(user, request);
  }

  @Post('logout')
  @HttpCode(HttpStatus.OK)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'User logout',
    description: 'Invalidate current session and clear authentication tokens',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Successfully logged out',
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid or expired token',
  })
  @UseGuards(JwtAuthGuard)
  @AllowUnverifiedEmail()
  async logout(@User() user: RequestUserType) {
    return this.authService.logout(user.sessionId);
  }

  @Post('register/partner')
  @Throttle({ default: { limit: 30, ttl: 60000 } })
  @ApiOperation({
    summary: 'Register partner',
    description: 'Register a new partner account',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Partner successfully registered',
    type: RegisterResponseDto,
  })
  @ApiConflictResponse({
    description: 'Email or tax number already exists',
  })
  @ApiBadRequestResponse({
    description: 'Invalid input data',
  })
  registerPartner(
    @Body() registerDto: PartnerRegisterDto,
  ): Promise<RegisterResponseDto> {
    return this.authService.partnerRegister(registerDto);
  }

  @Post('register/worker')
  @Throttle({ default: { limit: 30, ttl: 60000 } })
  @ApiOperation({
    summary: 'Register worker',
    description: 'Register a new worker account',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Worker successfully registered',
    type: RegisterResponseDto,
  })
  @ApiConflictResponse({
    description: 'Email already exists',
  })
  @ApiBadRequestResponse({
    description: 'Invalid registration code or input data',
  })
  registerWorker(
    @Body() registerDto: WorkerRegisterDto,
  ): Promise<RegisterResponseDto> {
    if (!registerDto.registrationCode)
      return this.authService.basicRegister(registerDto, Role.Worker);

    return this.authService.workerRegister(registerDto);
  }

  @Post('register/manager')
  @Throttle({ default: { limit: 30, ttl: 60000 } })
  @ApiOperation({
    summary: 'Register manager',
    description: 'Register a new manager account',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Manager successfully registered',
    type: RegisterResponseDto,
  })
  @ApiConflictResponse({
    description: 'Email already exists',
  })
  @ApiBadRequestResponse({
    description: 'Invalid registration code or input data',
  })
  registerManager(
    @Body() registerDto: ManagerRegisterDto,
  ): Promise<RegisterResponseDto> {
    return this.authService.managerRegister(registerDto);
  }

  @Post('refresh')
  @HttpCode(HttpStatus.OK)
  @UseGuards(JwtRefreshAuthGuard)
  @Throttle({ default: { limit: 3, ttl: 1000 } })
  @ApiOperation({
    summary: 'Refresh token',
    description: 'Get a new access token using refresh token',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Token refreshed successfully',
    type: LoginResponseDto,
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid or expired refresh token',
  })
  @ApiBadRequestResponse({
    description: 'Invalid refresh token format',
  })
  refresh(
    @User() user: LocalStrategyReturnType,
    @Body() _refreshTokenDto: RefreshTokenDto,
    @Req() request: Request,
  ): Promise<LoginResponseDto> {
    return this.authService.login(user, request);
  }

  @Post('generate-password-reset-token')
  @Throttle({ default: { limit: 30, ttl: 60000 } })
  @ApiOperation({
    summary: 'Generate password reset token',
    description: 'Generate a token for password reset',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Reset token generated and email sent',
  })
  @ApiBadRequestResponse({
    description: 'Invalid email address',
  })
  async generatePasswordResetToken(
    @Body() generatePasswordResetTokenDto: GeneratePasswordResetTokenDto,
  ) {
    const token = await this.authService.generatePasswordResetToken(
      generatePasswordResetTokenDto,
    );

    await this.mailerService.sendMail({
      to: generatePasswordResetTokenDto.email,
      subject: 'Password reset',
      html: passwordResetEmailTemplate(
        `https://auth.sstw.io/reset-password/${token}`,
      ),
    });
  }

  @Post('reset-password')
  @Throttle({ default: { limit: 30, ttl: 60000 } })
  @ApiOperation({
    summary: 'Reset password',
    description: 'Reset password using token',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Password successfully reset',
  })
  @ApiBadRequestResponse({
    description: 'Invalid or expired reset token',
  })
  @ApiUnauthorizedResponse({
    description: 'Token not found or user not found',
  })
  async resetPassword(@Body() passwordResetDto: PasswordResetDto) {
    return this.authService.resetPassword(passwordResetDto);
  }
}
