import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';

import { Auth<PERSON>ontroller } from './auth.controller';
import { AuthService } from './auth.service';
import { CredentialVerificationModule } from '../credential-verification/credential-verification.module';
import { DevicesModule } from '../devices/devices.module';
import { ManagersModule } from '../managers/managers.module';
import { PartnersModule } from '../partners/partners.module';
import { PasswordResetRequestsModule } from '../password-reset-requests/password-reset-requests.module';
import { RegistrationCodesModule } from '../registration-codes/registration-codes.module';
import { RegistrationRequestsModule } from '../registration-requests/registration-requests.module';
import { SessionsModule } from '../sessions/sessions.module';
import { UsersModule } from '../users/users.module';
import { UsersService } from '../users/users.service';
import { WorkersModule } from '../workers/workers.module';
import { JwtRefreshStrategy } from './strategies/jwt-refresh.strategy';
import { JwtStrategy } from './strategies/jwt.strategy';
import { LocalStrategy } from './strategies/local.strategy';
import { SecurityNotificationsModule } from '../security-notifications/security-notifications.module';
import { UpdatesModule } from '../updates/updates.module';

@Module({
  imports: [
    PassportModule,
    UsersModule,
    UpdatesModule,
    ManagersModule,
    WorkersModule,
    PartnersModule,
    RegistrationCodesModule,
    RegistrationRequestsModule,
    PasswordResetRequestsModule,
    SessionsModule,
    DevicesModule,
    CredentialVerificationModule,
    SecurityNotificationsModule,
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => {
        return {
          secret: configService.get('auth.jwtSecret'),
          signOptions: { expiresIn: '15m' },
        };
      },
      inject: [ConfigService],
    }),
  ],
  controllers: [AuthController],
  providers: [
    AuthService,
    LocalStrategy,
    JwtStrategy,
    UsersService,
    JwtRefreshStrategy,
  ],
  exports: [AuthService],
})
export class AuthModule {}
