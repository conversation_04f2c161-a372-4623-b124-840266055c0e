import { createSelectSchema } from 'drizzle-zod';
import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

import { activityHistory } from '@/modules/db/entities/activity-history.entity';

export const getActivityHistorySchema = createSelectSchema(activityHistory)
  .pick({
    id: true,
    activityType: true,
    happenedAt: true,
    metadata: true,
  })
  .extend({
    firstName: z.string(),
    lastName: z.string(),
    role: z.string(),
    projectName: z.string().optional(),
    workerId: z.string().optional(),
    managerId: z.string().optional(),
    projectId: z.string().optional(),
  });

export class ActivityHistoryDto extends createZodDto(
  getActivityHistorySchema,
) {}
