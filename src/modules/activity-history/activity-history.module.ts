import { Module, forwardRef } from '@nestjs/common';

import { ActivityHistoryController } from './activity-history.controller';
import { ActivityHistoryService } from './activity-history.service';
import { ManagersModule } from '../managers/managers.module';

@Module({
  imports: [forwardRef(() => ManagersModule)],
  controllers: [ActivityHistoryController],
  providers: [ActivityHistoryService],
  exports: [ActivityHistoryService],
})
export class ActivityHistoryModule {}
