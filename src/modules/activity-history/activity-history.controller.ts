import {
  Controller,
  Get,
  HttpStatus,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiQuery,
} from '@nestjs/swagger';

import { Roles } from '@/common/decorators/roles.decorator';
import { User } from '@/common/decorators/user.decorator';
import { Role, ManagerPermissionType } from '@/common/enums';
import { RolesGuard } from '@/common/guards/roles.guard';

import { ActivityHistoryDto } from './dto/activity-history.dto';
import { ActivityHistoryFilterParamsDto } from './dto/activity-history-filter-params.dto';
import { ActivityHistoryService } from './activity-history.service';
import { RequestUserType } from '../auth/dto/request-user.dto';
import { JwtAuthGuard } from '../auth/guards/jwt.guard';

@ApiTags('Activity History')
@ApiBearerAuth()
@Controller('activity-history')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(Role.Partner, {
  type: Role.Manager,
  settings: {
    permissionType: ManagerPermissionType.All,
  },
})
export class ActivityHistoryController {
  constructor(
    private readonly activityHistoryService: ActivityHistoryService,
  ) {}

  @Get()
  @ApiOperation({
    summary: 'Get all activity history',
    description:
      'Retrieve all activity history for the authenticated partner or manager. Includes employment changes, project assignments, and other tracked activities.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Activity history retrieved successfully',
    type: [ActivityHistoryDto],
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    enum: ['date', 'activityType'],
    description: 'Sort by date or activity type',
  })
  @ApiQuery({
    name: 'sortOrder',
    required: false,
    enum: ['asc', 'desc'],
    description: 'Sort order',
  })
  @ApiQuery({
    name: 'activityType',
    required: false,
    description: 'Filter by specific activity type',
  })
  @ApiQuery({
    name: 'role',
    required: false,
    enum: ['worker', 'manager'],
    description: 'Filter by role',
  })
  @ApiQuery({
    name: 'projectId',
    required: false,
    description: 'Filter by project ID',
  })
  findAll(
    @User() user: RequestUserType,
    @Query() filterParams?: ActivityHistoryFilterParamsDto,
  ): Promise<ActivityHistoryDto[]> {
    return this.activityHistoryService.findAll(user, filterParams);
  }
}
