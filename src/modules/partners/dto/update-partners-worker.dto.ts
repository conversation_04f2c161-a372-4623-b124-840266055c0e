import { createInsertSchema } from 'drizzle-zod';
import { createZodDto } from 'nestjs-zod';

import { workers } from '@/modules/db/entities/worker.entity';

export const updatePartnersWorkerSchema = createInsertSchema(workers)
  .pick({
    profession: true,
    hourlyRate: true,
    professionId: true,
  })
  .partial();

export class UpdatePartnersWorkerDto extends createZodDto(
  updatePartnersWorkerSchema,
) {}
