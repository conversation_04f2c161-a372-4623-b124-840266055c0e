import { Module, forwardRef } from '@nestjs/common';

import { PartnersController } from './partners.controller';
import { PartnersService } from './partners.service';
import { ManagersModule } from '../managers/managers.module';
import { ProjectsModule } from '../projects/projects.module';
import { UpdatesModule } from '../updates/updates.module';
import { UsersModule } from '../users/users.module';
import { WorkersModule } from '../workers/workers.module';

@Module({
  imports: [
    forwardRef(() => WorkersModule),
    forwardRef(() => ManagersModule),
    forwardRef(() => UsersModule),
    forwardRef(() => ProjectsModule),
    forwardRef(() => UpdatesModule),
  ],
  controllers: [PartnersController],
  providers: [PartnersService],
  exports: [PartnersService],
})
export class PartnersModule {}
