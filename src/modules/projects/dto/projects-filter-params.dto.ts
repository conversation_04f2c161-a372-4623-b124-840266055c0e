import { ApiProperty } from '@nestjs/swagger';
import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

export const projectsFilterParamsSchema = z.object({
  sortBy: z.enum(['date', 'name']).optional(),
  sortOrder: z.enum(['asc', 'desc']).optional(),
});

export class ProjectsFilterParamsDto extends createZodDto(
  projectsFilterParamsSchema,
) {
  @ApiProperty({
    description: 'Sort projects by specified property',
    required: false,
    example: 'date',
  })
  sortBy?: 'date' | 'name';

  @ApiProperty({
    description: 'Sort order direction',
    required: false,
    example: 'desc',
  })
  sortOrder?: 'asc' | 'desc';
}
