import { createInsertSchema } from 'drizzle-zod';
import { createZodDto } from 'nestjs-zod';

import { projects } from '@/modules/db/entities/project.entity';

export const updateProjectSchema = createInsertSchema(projects)
  .omit({ partnerId: true, id: true, createdAt: true })
  .partial();

export const updatePartnerProjectSchema = updateProjectSchema.omit({
  isActive: true,
});

export class UpdateProjectDto extends createZodDto(updateProjectSchema) {}

export class UpdatePartnerProjectDto extends createZodDto(
  updatePartnerProjectSchema,
) {}
