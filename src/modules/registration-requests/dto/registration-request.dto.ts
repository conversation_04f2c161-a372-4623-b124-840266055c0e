import { createSelectSchema } from 'drizzle-zod';
import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

import { registrationRequests } from '@/modules/db/entities/registration-request.entity';

export const registrationRequestSchema = z.object({
  id: z.string(),
  code: z.string().nullable(),
  requestedAt: z.preprocess(
    (val) => (typeof val === 'string' ? new Date(val) : val),
    z.date(),
  ),
  firstName: z.string().nullable(),
  lastName: z.string().nullable(),
  phoneNumber: z.string().nullable(),
  workerId: z.string().nullable(),
  avatarId: z.string().nullable(),
  profession: z.string().nullable(),
});

export const getRegistrationRequestSchema =
  createSelectSchema(registrationRequests);

export const workersRegistrationRequestSchema =
  getRegistrationRequestSchema.extend({
    registrationCode: z.string(),
    companyName: z.string().nullable(),
    partnerFullName: z.string(),
    partnerEmail: z.string(),
    companyAvatarId: z.string().nullable(),
  });

export class RegistrationRequestDto extends createZodDto(
  registrationRequestSchema,
) {}

export class WorkersRegistrationRequest extends createZodDto(
  workersRegistrationRequestSchema,
) {}
