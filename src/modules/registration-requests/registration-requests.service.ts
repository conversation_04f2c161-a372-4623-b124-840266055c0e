import {
  ForbiddenException,
  Inject,
  Injectable,
  NotFoundException,
  forwardRef,
} from '@nestjs/common';
import { and, eq, like, or } from 'drizzle-orm';

import { Role } from '@/common/enums';
import { Forwarded } from '@/common/types';

import { RequestUserType } from '../auth/dto/request-user.dto';
import { Database } from '../db/db.module';
import { managers } from '../db/entities/manager.entity';
import { registrationCodes } from '../db/entities/registration-code.entity';
import { registrationRequests } from '../db/entities/registration-request.entity';
import { users } from '../db/entities/user.entity';
import { workers } from '../db/entities/worker.entity';
import { ManagersService } from '../managers/managers.service';
import { NotificationsService } from '../notifications/notifications.service';
import { UsersService } from '../users/users.service';
import { WorkersService } from '../workers/workers.service';
import { CreateRegistrationRequestDto } from './dto/create-registration-request.dto';
import { RegistrationRequestDto } from './dto/registration-request.dto';
import { EmploymentHistoryService } from '../employment-history/employment-history.service';
import { RegistrationCodesService } from '../registration-codes/registration-codes.service';
import { WorkerFilterParamsDto } from '../workers/dto/worker-filter-params.dto';
import { UpdatesGateway } from '../updates/updates.gateway';
import { UpdatesEvents } from '../updates/updates.types';
@Injectable()
export class RegistrationRequestsService {
  constructor(
    @Inject('DB') private readonly db: Database,
    @Inject(forwardRef(() => UsersService))
    private readonly usersService: Forwarded<UsersService>,
    @Inject(forwardRef(() => WorkersService))
    private readonly workersService: Forwarded<WorkersService>,
    private readonly employmentHistoryService: EmploymentHistoryService,
    private readonly notificationsService: NotificationsService,
    @Inject(forwardRef(() => RegistrationCodesService))
    private readonly registrationCodesService: RegistrationCodesService,
    private readonly managersService: ManagersService,
    @Inject(forwardRef(() => UpdatesGateway))
    private readonly updatesGateway: Forwarded<UpdatesGateway>,
  ) {}

  async create(createRegistrationRequestDto: CreateRegistrationRequestDto) {
    const [request] = await this.db
      .insert(registrationRequests)
      .values(createRegistrationRequestDto)
      .returning({ id: registrationRequests.id });

    const registrationCode = await this.registrationCodesService.findOne(
      createRegistrationRequestDto.registrationCodeId,
    );

    if (!registrationCode) throw new NotFoundException('Code not found');
    console.log('Sending notification for registration request', {
      title: 'Registration Request Received',
      body: `A new ${
        createRegistrationRequestDto.managerId ? 'manager' : 'worker'
      } has registered for your company`,
      receiverUserId: registrationCode.partner.userId,
      topic: 'registration-request-received',
      data: {
        workerId: createRegistrationRequestDto.workerId,
        managerId: createRegistrationRequestDto.managerId,
      },
    });
    await this.notificationsService
      .send({
        title: 'Registration Request Received',
        body: `A new ${
          createRegistrationRequestDto.managerId ? 'manager' : 'worker'
        } has registered for your company`,
        receiverUserId: registrationCode.partner.userId,
        topic: 'registration-request-received',
        data: {
          workerId: createRegistrationRequestDto.workerId,
          managerId: createRegistrationRequestDto.managerId,
        },
      })
      .catch((e) =>
        console.error(
          'Error while sending notification for registration request: ',
          e,
        ),
      );

    await this.updatesGateway.sendMessage({
      type: UpdatesEvents.REGISTRATION_REQUEST_RECEIVED,
      payload: {
        requestId: request.id,
        partnerId: registrationCode.partnerId,
      },
    });
  }

  async getAllForUser(
    user: RequestUserType,
    filterParams?: WorkerFilterParamsDto,
  ) {
    const whereClauses = [
      filterParams?.firstName
        ? or(
            like(users.firstName, filterParams.firstName),
            like(users.lastName, filterParams.firstName),
          )
        : undefined,
      filterParams?.lastName
        ? or(
            like(users.firstName, filterParams.lastName),
            like(users.lastName, filterParams.lastName),
          )
        : undefined,
    ];

    const managerId = user.role === Role.Manager ? user.entityId : null;
    const partnerId = user.role === Role.Partner ? user.entityId : null;

    return this.db
      .select({
        id: registrationRequests.id,
        code: registrationCodes.code,
        requestedAt: registrationRequests.requestedAt,
        firstName: users.firstName,
        lastName: users.lastName,
        avatarId: users.avatarId,
        phoneNumber: users.phoneNumber,
        profession: registrationCodes.profession,
        workerId: registrationRequests.workerId,
        managerId: registrationRequests.managerId,
      })
      .from(registrationRequests)
      .leftJoin(
        registrationCodes,
        eq(registrationRequests.registrationCodeId, registrationCodes.id),
      )
      .leftJoin(workers, eq(registrationRequests.workerId, workers.id))
      .leftJoin(managers, eq(registrationRequests.managerId, managers.id))
      .leftJoin(
        users,
        or(eq(workers.userId, users.id), eq(managers.userId, users.id)),
      )
      .where(
        and(
          managerId
            ? eq(registrationCodes.managerId, managerId)
            : partnerId
              ? eq(registrationCodes.partnerId, partnerId)
              : undefined,
          eq(registrationRequests.status, 'pending'),
          ...whereClauses,
        ),
      );
  }

  findOne(id: string) {
    return this.db.query.registrationRequests.findFirst({
      where: (registrationRequests, { eq }) => eq(registrationRequests.id, id),
      with: {
        registrationCode: true,
      },
    });
  }

  async findPendingForWorker(workerId: string) {
    const request = await this.db.query.registrationRequests.findFirst({
      where: (registrationRequests, { eq, and }) =>
        and(
          eq(registrationRequests.workerId, workerId),
          eq(registrationRequests.status, 'pending'),
        ),
      with: {
        registrationCode: {
          columns: {
            code: true,
          },
          with: {
            partner: {
              columns: {
                companyName: true,
              },
              with: {
                user: {
                  columns: {
                    firstName: true,
                    lastName: true,
                    email: true,
                    avatarId: true,
                  },
                },
              },
            },
          },
        },
      },
    });
    if (!request) throw new NotFoundException('Registration Request not found');
    const { registrationCode, ...rest } = request;
    return {
      ...rest,
      registrationCode: registrationCode.code,
      companyName: registrationCode.partner.companyName,
      partnerFullName: `${registrationCode.partner.user.firstName} ${registrationCode.partner.user.lastName}`,
      partnerEmail: registrationCode.partner.user.email,
      companyAvatarId: registrationCode.partner.user.avatarId,
    };
  }

  async changeStatus(
    id: string,
    user: RequestUserType,
    status: 'approved' | 'rejected',
  ): Promise<{ request: RegistrationRequestDto }> {
    const usersRegistrationRequests = await this.getAllForUser(user);
    const request = usersRegistrationRequests.find((r) => r.id === id);

    if (!request) throw new NotFoundException('Request not found');

    const { workerId, managerId } = request;
    let partnerId = user.role === Role.Partner ? user.entityId : null;
    if (!partnerId && user.role === Role.Manager) {
      partnerId =
        (await this.managersService.getPartnerId(user.entityId)) ?? null;
    }
    if (!partnerId)
      throw new ForbiddenException(
        'You are not allowed to change the status of this request',
      );

    await this.db
      .update(registrationRequests)
      .set({
        status,
        approvedBy: status === 'approved' ? user.id : undefined,
        approvedAt: status === 'approved' ? new Date() : undefined,
        rejectedAt: status === 'rejected' ? new Date() : undefined,
      })
      .where(eq(registrationRequests.id, id));

    if (workerId) {
      const worker = await this.workersService.findOne(workerId);
      if (!worker) throw new NotFoundException('Worker not found');

      await this.workersService.changeApprovalState(workerId, status);
      if (status === 'approved') {
        await this.employmentHistoryService.create({
          workerId,
          operationAuthorId: user.id,
          partnerId,
          change: 'hire',
        });
        await this.workersService.changeEmploymentStatus(workerId, 'active');
      }
      await this.notificationsService.send({
        title: 'Registration Request Status Changed',
        body: `Your registration request has been ${status} by your partner`,
        receiverUserId: worker.userId,
        topic: 'registration-request-status-changed',
        data: {
          status,
        },
      });
    }

    if (managerId) {
      const manager = await this.managersService.findOne(managerId);
      if (!manager) throw new NotFoundException('Manager not found');

      await this.managersService.changeApprovalState(managerId, status);

      if (status === 'approved') {
        await this.employmentHistoryService.create({
          managerId,
          operationAuthorId: user.id,
          partnerId,
          change: 'hire',
        });
      }

      await this.notificationsService.send({
        title: 'Registration Request Status Changed',
        body: `Your registration request has been ${status} by your partner`,
        receiverUserId: manager.userId,
        topic: 'registration-request-status-changed',
        data: {
          status,
        },
      });
    }

    return { request };
  }

  async approve(id: string, user: RequestUserType) {
    const { request } = await this.changeStatus(id, user, 'approved');
    const fullRequest = await this.findOne(request.id);
    if (!fullRequest) throw new NotFoundException('Request not found');
  }

  async rejectAllPendingForCode(codeId: string) {
    await this.db
      .update(registrationRequests)
      .set({ status: 'rejected' })
      .where(
        and(
          eq(registrationRequests.registrationCodeId, codeId),
          eq(registrationRequests.status, 'pending'),
        ),
      );
  }

  async reject(id: string, user: RequestUserType) {
    await this.changeStatus(id, user, 'rejected');
  }

  async deleteAllPendingForWorker(workerId: string) {
    const requests = await this.db.query.registrationRequests.findMany({
      where: (registrationRequests, { eq }) =>
        eq(registrationRequests.workerId, workerId),
    });
    await Promise.all(
      requests.map((request) =>
        this.db
          .delete(registrationRequests)
          .where(
            and(
              eq(registrationRequests.id, request.id),
              eq(registrationRequests.status, 'pending'),
            ),
          ),
      ),
    );
  }
}
