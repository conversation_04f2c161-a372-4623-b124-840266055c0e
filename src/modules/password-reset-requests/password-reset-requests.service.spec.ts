import { Test, TestingModule } from '@nestjs/testing';

import { PasswordResetRequestsService } from './password-reset-requests.service';

describe('PasswordResetRequestsService', () => {
  let service: PasswordResetRequestsService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [PasswordResetRequestsService],
    }).compile();

    service = module.get<PasswordResetRequestsService>(
      PasswordResetRequestsService,
    );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
