import { Inject, Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { eq, lt, or } from 'drizzle-orm';

import { hashTokenConsistently } from '@/common/utils/hash';

import { Database } from '../db/db.module';
import { CreatePasswordResetRequestDto } from './dto/create-password-reset-request.dto';
import { passwordResetRequests } from '../db/entities/password-reset-request.entity';

@Injectable()
export class PasswordResetRequestsService {
  private readonly logger = new Logger(PasswordResetRequestsService.name);

  constructor(@Inject('DB') private readonly db: Database) {}

  create(createPasswordResetRequestDto: CreatePasswordResetRequestDto) {
    return this.db
      .insert(passwordResetRequests)
      .values(createPasswordResetRequestDto);
  }

  markAsUsed(id: string) {
    return this.db
      .update(passwordResetRequests)
      .set({
        isUsed: true,
      })
      .where(eq(passwordResetRequests.id, id));
  }

  async findByToken(token: string) {
    const hashedToken = await hashTokenConsistently(token);
    const request = await this.db.query.passwordResetRequests.findFirst({
      where: (passwordResetRequests, { eq, and }) =>
        and(
          eq(passwordResetRequests.isUsed, false),
          eq(passwordResetRequests.hashedToken, hashedToken),
        ),
    });

    return request;
  }

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async cleanupExpiredPasswordResetRequests() {
    try {
      const result = await this.db
        .delete(passwordResetRequests)
        .where(
          or(
            lt(passwordResetRequests.expiresAt, new Date()),
            eq(passwordResetRequests.isUsed, true),
          ),
        );

      this.logger.log(
        `Cleaned up ${result.rowCount} expired or used password reset requests`,
      );
    } catch (error) {
      this.logger.error('Failed to clean up password reset requests', error);
    }
  }
}
