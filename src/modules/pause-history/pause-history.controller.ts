import { Controller, Get, HttpStatus, Param, UseGuards } from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiForbiddenResponse,
  ApiNotFoundResponse,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';

import { User } from '@/common/decorators/user.decorator';

import { PauseHistoryDto } from './dto/pause-history.dto';
import { PauseHistoryService } from './pause-history.service';
import { RequestUserType } from '../auth/dto/request-user.dto';
import { JwtAuthGuard } from '../auth/guards/jwt.guard';

@ApiTags('Pause History')
@ApiBearerAuth()
@Controller('pause-history')
@UseGuards(JwtAuthGuard)
export class PauseHistoryController {
  constructor(private readonly pauseHistoryService: PauseHistoryService) {}

  @Get('/reports/:reportId')
  @ApiOperation({
    summary: 'Get report pause history',
    description: 'Retrieve all pause events for a specific daily report',
  })
  @ApiParam({
    name: 'reportId',
    description: 'Daily report unique identifier',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Pause history retrieved successfully',
    type: [PauseHistoryDto],
  })
  @ApiUnauthorizedResponse({ description: 'Not authenticated' })
  @ApiForbiddenResponse({ description: 'Not authorized to view this report' })
  @ApiNotFoundResponse({ description: 'Report not found' })
  findAllByReportId(
    @Param('reportId') reportId: string,
    @User() user: RequestUserType,
  ): Promise<PauseHistoryDto[]> {
    return this.pauseHistoryService.findAllByReportId(reportId, {
      type: user.role,
      entityId: user.entityId,
    });
  }
}
