import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { and, eq, isNull } from 'drizzle-orm';

import { CreatePauseHistoryDto } from './dto/create-pause-history.dto';
import { UpdatePauseHistoryDto } from './dto/update-pause-history.dto';
import { Database } from '../db/db.module';
import { dailyReports } from '../db/entities/daily-report.entity';
import { pauseHistory } from '../db/entities/pause-history.entity';
import { workers } from '../db/entities/worker.entity';

@Injectable()
export class PauseHistoryService {
  constructor(@Inject('DB') private readonly db: Database) {}

  create(createPauseHistoryDto: CreatePauseHistoryDto) {
    return this.db.insert(pauseHistory).values(createPauseHistoryDto);
  }

  update(id: string, updatePauseHistoryDto: UpdatePauseHistoryDto) {
    return this.db.transaction(async (tx) => {
      const pauseHistoryEntry = await tx.query.pauseHistory.findFirst({
        where: (pauseHistory, { eq }) => eq(pauseHistory.id, id),
      });

      if (!pauseHistoryEntry) {
        throw new NotFoundException('Pause history entry not found');
      }

      return tx
        .update(pauseHistory)
        .set(updatePauseHistoryDto)
        .where(eq(pauseHistory.id, id));
    });
  }

  findAllByReportId(
    reportId: string,
    userRoleInfo?: {
      type: 'partner' | 'manager' | 'worker';
      entityId: string;
    },
  ) {
    const userWhereClauses =
      userRoleInfo?.type === 'partner'
        ? eq(workers.partnerId, userRoleInfo.entityId)
        : userRoleInfo?.type === 'worker'
          ? eq(workers.id, userRoleInfo.entityId)
          : undefined;

    return this.db
      .select({
        id: pauseHistory.id,
        pauseStart: pauseHistory.pauseStart,
        pauseEnd: pauseHistory.pauseEnd,
        createdAt: pauseHistory.createdAt,
      })
      .from(pauseHistory)
      .leftJoin(dailyReports, eq(pauseHistory.dailyReportId, dailyReports.id))
      .leftJoin(workers, eq(dailyReports.authorId, workers.id))
      .where(and(eq(pauseHistory.dailyReportId, reportId), userWhereClauses));
  }

  findActiveByReportId(reportId: string) {
    return this.db.query.pauseHistory.findFirst({
      where: (pauseHistory, { eq }) =>
        and(
          eq(pauseHistory.dailyReportId, reportId),
          isNull(pauseHistory.pauseEnd),
        ),
    });
  }
}
