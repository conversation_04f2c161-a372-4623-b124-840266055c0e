import { Module, forwardRef } from '@nestjs/common';

import { PauseHistoryController } from './pause-history.controller';
import { PauseHistoryService } from './pause-history.service';
import { UsersModule } from '../users/users.module';

@Module({
  imports: [forwardRef(() => UsersModule)],
  controllers: [PauseHistoryController],
  providers: [PauseHistoryService],
  exports: [PauseHistoryService],
})
export class PauseHistoryModule {}
