import { Test, TestingModule } from '@nestjs/testing';

import { PresenceValidationsService } from './presence-validations.service';

describe('PresenceValidationsService', () => {
  let service: PresenceValidationsService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [PresenceValidationsService],
    }).compile();

    service = module.get<PresenceValidationsService>(
      PresenceValidationsService,
    );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
