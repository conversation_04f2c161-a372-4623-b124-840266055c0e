import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

export const presenceValidationDataSchema = z.object({
  geoCoordinates: z.string().optional(),
  sentAt: z.preprocess(
    (val) => (typeof val === 'string' ? new Date(val) : val),
    z.date(),
  ),
  photoId: z.string().optional(),
});

export class PresenceValidationDataDto extends createZodDto(
  presenceValidationDataSchema,
) {}
