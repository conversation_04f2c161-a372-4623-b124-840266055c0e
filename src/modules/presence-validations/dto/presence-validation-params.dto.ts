import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

export const presenceValidationParams = z.object({
  requiresPhoto: z.boolean(),
  requiresGeo: z.boolean(),
  reason: z.enum(['start', 'finish', 'pause', 'unpause', 'check']).optional(),
});

export class PresenceValidationParamsWithWorkerIdsDto extends createZodDto(
  presenceValidationParams.extend({
    workerIds: z.array(z.string()),
  }),
) {}

export class PresenceValidationParamsDto extends createZodDto(
  presenceValidationParams,
) {}
