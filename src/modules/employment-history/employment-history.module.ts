import { Module, forwardRef } from '@nestjs/common';

import { EmploymentHistoryController } from './employment-history.controller';
import { EmploymentHistoryService } from './employment-history.service';
import { ManagersModule } from '../managers/managers.module';
import { UsersModule } from '../users/users.module';

@Module({
  imports: [forwardRef(() => UsersModule), forwardRef(() => ManagersModule)],
  controllers: [EmploymentHistoryController],
  providers: [EmploymentHistoryService],
  exports: [EmploymentHistoryService],
})
export class EmploymentHistoryModule {}
