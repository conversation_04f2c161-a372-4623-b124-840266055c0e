import { createSelectSchema } from 'drizzle-zod';
import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

import { employmentHistory } from '@/modules/db/entities/employment-history.entity';

export const getEmploymentHistorySchema = createSelectSchema(employmentHistory)
  .pick({
    change: true,
    happenedAt: true,
  })
  .extend({
    firstName: z.string(),
    lastName: z.string(),
    role: z.string(),
  });

export class EmploymentHistoryDto extends createZodDto(
  getEmploymentHistorySchema,
) {}
