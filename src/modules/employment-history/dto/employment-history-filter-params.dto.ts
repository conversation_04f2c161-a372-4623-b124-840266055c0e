import { ApiProperty } from '@nestjs/swagger';
import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

export const employmentHistoryFilterParamsSchema = z.object({
  sortBy: z.enum(['date', 'change']).optional(),
  sortOrder: z.enum(['asc', 'desc']).optional(),
  change: z.enum(['hire', 'quit', 'terminated']).optional(),
});

export class EmploymentHistoryFilterParamsDto extends createZodDto(
  employmentHistoryFilterParamsSchema,
) {
  @ApiProperty({
    description: 'Sort employment history entries by specified property',
    required: false,
    example: 'date',
  })
  sortBy?: 'date' | 'change';

  @ApiProperty({
    description: 'Sort order direction',
    required: false,
    example: 'desc',
  })
  sortOrder?: 'asc' | 'desc';

  @ApiProperty({
    description: 'Filter by change type',
    required: false,
    example: 'hire',
  })
  change?: 'hire' | 'quit' | 'terminated';
}
