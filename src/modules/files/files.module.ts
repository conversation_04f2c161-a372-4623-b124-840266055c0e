import { Module, forwardRef } from '@nestjs/common';

import { FilesController } from './files.controller';
import { FilesService } from './files.service';
import { FilePermissionsModule } from '../file-permissions/file-permissions.module';
import { ManagersModule } from '../managers/managers.module';
import { PartnersModule } from '../partners/partners.module';
import { UsersModule } from '../users/users.module';
import { WorkersModule } from '../workers/workers.module';

@Module({
  imports: [
    FilePermissionsModule,
    forwardRef(() => WorkersModule),
    forwardRef(() => ManagersModule),
    forwardRef(() => PartnersModule),
    forwardRef(() => UsersModule),
  ],
  controllers: [FilesController],
  providers: [FilesService],
  exports: [FilesService],
})
export class FilesModule {}
