import { ApiProperty } from '@nestjs/swagger';
import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

export const uploadFileParamsSchema = z
  .object({
    security: z.enum(['public', 'private']).optional(),
    scope: z.enum(['user', 'partner']).optional().default('user'),
  })
  .refine(
    (data) => {
      if (data.security === 'public' && data.scope !== 'user') {
        return false;
      }
      return true;
    },
    {
      message:
        'Scope other than "user" is not allowed when security is set to public',
      path: ['scope'],
    },
  );

export class UploadFileParamsDto extends createZodDto(uploadFileParamsSchema) {
  @ApiProperty({
    description:
      'File security, determines whether the file is publicly accessible',
    required: false,
    enum: ['public', 'private'],
  })
  security?: 'public' | 'private';

  @ApiProperty({
    description: 'File scope, determines who can access the file',
    enum: ['user', 'partner'],
  })
  scope: 'user' | 'partner' = 'user';
}
