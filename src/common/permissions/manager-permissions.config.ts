enum ResourceType {
  Manager = 'manager',
  Worker = 'worker',
  Project = 'project',
}

export enum ManagerPermissionType {
  All = 'all',
  ProjectManager = 'project_manager',
}

export interface ManagerPermissionConfig {
  resources: ResourceType[];
  canEditCompanyInfo: boolean;
  canManageAllWorkers: boolean;
  canManageAllProjects: boolean;
  canCreateProjects: boolean;
  canApproveWorkers: boolean;
  canFireWorkers: boolean;
}

export const MANAGER_PERMISSIONS: Record<
  ManagerPermissionType,
  ManagerPermissionConfig
> = {
  [ManagerPermissionType.All]: {
    resources: [
      ResourceType.Worker,
      ResourceType.Project,
      ResourceType.Manager,
    ],
    canEditCompanyInfo: false,
    canManageAllWorkers: true,
    canManageAllProjects: true,
    canCreateProjects: true,
    canApproveWorkers: true,
    canFireWorkers: true,
  },
  [ManagerPermissionType.ProjectManager]: {
    resources: [ResourceType.Worker, ResourceType.Project],
    canEditCompanyInfo: false,
    canManageAllWorkers: false,
    canManageAllProjects: false,
    canCreateProjects: false,
    canApproveWorkers: true,
    canFireWorkers: true,
  },
};

export function canManagerPerform(
  permissionType: ManagerPermissionType,
  action: keyof ManagerPermissionConfig,
): boolean {
  return !!MANAGER_PERMISSIONS[permissionType][action];
}

export function canManagerAccessResource(
  permissionType: ManagerPermissionType,
  resource: ResourceType,
): boolean {
  return MANAGER_PERMISSIONS[permissionType].resources.includes(resource);
}
