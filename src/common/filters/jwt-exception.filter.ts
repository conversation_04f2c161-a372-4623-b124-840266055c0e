import {
  ArgumentsHost,
  Catch,
  ExceptionFilter,
  UnauthorizedException,
} from '@nestjs/common';
import { JsonWebTokenError, TokenExpiredError } from '@nestjs/jwt';
import { Response } from 'express';

@Catch(JsonWebTokenError, UnauthorizedException)
export class JwtExceptionFilter implements ExceptionFilter {
  catch(
    exception: JsonWebTokenError | UnauthorizedException,
    host: ArgumentsHost,
  ) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();

    if (exception instanceof TokenExpiredError) {
      return response.status(401).json({
        statusCode: 401,
        message: 'Token has expired',
        error: 'TokenExpired',
      });
    }

    if (exception instanceof JsonWebTokenError) {
      return response.status(401).json({
        statusCode: 401,
        message: 'Invalid token',
        error: 'InvalidToken',
      });
    }

    return response.status(401).json({
      statusCode: 401,
      message: 'Unauthorized',
      error: 'Unauthorized',
    });
  }
}
