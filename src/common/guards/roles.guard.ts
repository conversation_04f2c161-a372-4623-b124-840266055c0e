import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Request } from 'express';

import { ManagersService } from '@/modules/managers/managers.service';
import { UsersService } from '@/modules/users/users.service';

import { ROLES_KEY, RolesType } from '../decorators/roles.decorator';
import { Role } from '../enums';

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private readonly usersService: UsersService,
    private readonly managersService: ManagersService,
  ) {}

  async canActivate(ctx: ExecutionContext): Promise<boolean> {
    const requiredRoles = this.reflector.getAllAndOverride<RolesType[]>(
      ROLES_KEY,
      [ctx.getHandler(), ctx.getClass()],
    );
    if (!requiredRoles) {
      return true;
    }
    const request: Request = ctx.switchToHttp().getRequest();
    const { user } = request;
    if (!user) {
      return false;
    }
    const userRole = requiredRoles.find((role) =>
      typeof role === 'string' ? user.role === role : role.type === user.role,
    );
    if (!userRole) {
      return false;
    }
    if (typeof userRole !== 'string') {
      const { roleInfo } = await this.usersService.getUserMetaData(user.id);

      if (userRole.type === Role.Worker && roleInfo.type === Role.Worker) {
        if (userRole.settings?.mustBeEmployed) {
          if (!user.entityId) return false;
        }
        if (userRole.settings?.mustBeApproved) {
          if (!roleInfo.approvalState) return false;
        }
      }

      if (userRole.type === Role.Manager && roleInfo.type === Role.Manager) {
        if (userRole.settings?.permissionType) {
          const manager = await this.managersService.findOneByUserId(user.id);
          if (!manager) return false;

          if (userRole.settings.permissionType !== manager.permissionType) {
            return false;
          }
        }
        if (roleInfo.employmentStatus) {
          if (roleInfo.employmentStatus !== 'active') {
            return false;
          }
        }
        if (roleInfo.approvalState) {
          if (roleInfo.approvalState !== 'approved') {
            return false;
          }
        }
      }
    }
    return true;
  }
}
