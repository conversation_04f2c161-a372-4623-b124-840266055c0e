import { Injectable } from '@nestjs/common';
import { ThrottlerGuard } from '@nestjs/throttler';

@Injectable()
export class CustomThrottlerGuard extends ThrottlerGuard {
  protected async getTracker(req: Record<string, any>): Promise<string> {
    if (req.user && req.user.id) {
      return `${req.ip}-${req.user.id}`;
    }

    const userAgent = req.headers['user-agent'] || '';
    return `${req.ip}-${userAgent.substring(0, 20)}`;
  }
}
