import {
  CanActivate,
  ExecutionContext,
  Inject,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Request } from 'express';

import { Database } from '@/modules/db/db.module';
import { ManagerDto } from '@/modules/managers/dto/manager.dto';
import { ManagersService } from '@/modules/managers/managers.service';
import { ProjectDto } from '@/modules/projects/dto/project.dto';
import { ProjectsService } from '@/modules/projects/projects.service';
import { WorkerDto } from '@/modules/workers/dto/worker.dto';
import { WorkersService } from '@/modules/workers/workers.service';

import { MANAGEMENT_KEY } from '../decorators/management.decorator';
import {
  ManagerPermissionType,
  canManagerAccessResource,
  canManagerPerform,
} from '../permissions/manager-permissions.config';

@Injectable()
export class ManagementGuard implements CanActivate {
  constructor(
    private readonly reflector: Reflector,
    private readonly workersService: WorkersService,
    private readonly managersService: ManagersService,
    private readonly projectsService: ProjectsService,
    @Inject('DB') private readonly db: Database,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const { resource, atLeastOne } = this.reflector.get<{
      resource: ResourceType | ResourceType[];
      atLeastOne: boolean;
    }>(MANAGEMENT_KEY, context.getHandler());
    if (!resource) {
      throw new UnauthorizedException('Resource type metadata is missing.');
    }

    const request: Request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user) {
      throw new UnauthorizedException('User or target resource ID is missing.');
    }

    if (user.role === 'manager') {
      const manager = await this.managersService.findOneByUserId(user.id);
      if (
        manager &&
        manager.employmentStatus === 'active' &&
        manager.approvalState === 'approved'
      ) {
        if (Array.isArray(resource)) {
          const hasAccess = resource.every((r) =>
            canManagerAccessResource(
              manager.permissionType as ManagerPermissionType,
              r,
            ),
          );
          if (!hasAccess) {
            throw new UnauthorizedException(
              'You do not have permission to access these resources.',
            );
          }
        } else {
          if (
            !canManagerAccessResource(
              manager.permissionType as ManagerPermissionType,
              resource,
            )
          ) {
            throw new UnauthorizedException(
              'You do not have permission to access this resource.',
            );
          }
        }
      }
    }

    if (Array.isArray(resource)) {
      const resourceData = await Promise.all(
        resource.map((r) => {
          const targetId = request.params[`${r}Id`];
          if (!targetId) {
            throw new UnauthorizedException(
              'Target resource ID is missing. (' + r + ')',
            );
          }
          return this.getResourceById(r, targetId);
        }),
      );
      if (!resourceData.every((r) => r)) {
        throw new UnauthorizedException('Resource not found.');
      }

      const validateArray = async (_r: ResourceType, idx: number) =>
        await this.validateOwnership(
          user?.entityId,
          resourceData[idx] as Resource,
        );

      const results = await Promise.all(resource.map(validateArray));
      return atLeastOne ? results.some(Boolean) : results.every(Boolean);
    } else {
      const resourceData = await this.getResourceById(
        resource,
        request.params[`${resource}Id`],
      );
      if (!resourceData) {
        throw new UnauthorizedException('Resource not found.');
      }

      const isAuthorized = await this.validateOwnership(
        user?.entityId,
        resourceData,
      );
      if (!isAuthorized) {
        throw new UnauthorizedException(
          'You do not have permission to manage this resource.',
        );
      }
    }

    return true;
  }

  private async getResourceById(
    resource: ResourceType,
    id: string,
  ): Promise<Resource | null> {
    switch (resource) {
      case ResourceType.Manager:
        const manager = await this.managersService.findOne(id);
        if (!manager) return null;
        return {
          type: resource,
          entity: manager,
        };
      case ResourceType.Worker:
        const worker = await this.workersService.findOne(id);
        if (!worker) return null;
        return {
          type: resource,
          entity: worker,
        };
      case ResourceType.Project:
        const project = await this.projectsService.findOne(id);
        if (!project) return null;
        return {
          type: resource,
          entity: project,
        };
      default:
        return null;
    }
  }

  private async validateOwnership(
    userId: string | undefined | null,
    resource: Resource,
  ): Promise<boolean> {
    if (!userId) return false;

    if (resource.type === 'manager' && resource.entity.partnerId === userId) {
      return true;
    }

    if (resource.type === 'worker') {
      if (resource.entity.partnerId === userId) return true;

      const manager = await this.managersService.findOne(userId);
      if (
        manager &&
        manager.partnerId === resource.entity.partnerId &&
        manager.employmentStatus === 'active' &&
        manager.approvalState === 'approved'
      ) {
        // Manager with 'all' permission type can access any worker
        if (manager.permissionType === ManagerPermissionType.All) {
          return canManagerPerform(
            ManagerPermissionType.All,
            'canManageAllWorkers',
          );
        }

        if (manager.permissionType === ManagerPermissionType.ProjectManager) {
          if (resource.entity.managerId === manager.id) return true;

          if (resource.entity.projectId) {
            return await this.checkManagerProjectAccess(
              manager.id,
              resource.entity.projectId,
            );
          }
        }
      }
    }

    if (resource.type === 'project') {
      if (resource.entity.partnerId === userId) return true;

      const manager = await this.managersService.findOne(userId);
      if (manager) {
        if (manager.permissionType === ManagerPermissionType.All) {
          return canManagerPerform(
            ManagerPermissionType.All,
            'canManageAllProjects',
          );
        }

        if (manager.permissionType === ManagerPermissionType.ProjectManager) {
          return await this.checkManagerProjectAccess(
            manager.id,
            resource.entity.id,
          );
        }
      }
    }

    return false;
  }

  private async checkManagerProjectAccess(
    managerId: string,
    projectId: string,
  ): Promise<boolean> {
    const projectManager = await this.db.query.projectManagers.findFirst({
      where: (pm, { and, eq }) =>
        and(eq(pm.managerId, managerId), eq(pm.projectId, projectId)),
    });
    return !!projectManager;
  }
}

export enum ResourceType {
  Manager = 'manager',
  Worker = 'worker',
  Project = 'project',
}

type Resource =
  | {
      type: ResourceType.Manager;
      entity: ManagerDto;
    }
  | {
      type: ResourceType.Worker;
      entity: WorkerDto;
    }
  | {
      type: ResourceType.Project;
      entity: ProjectDto;
    };
