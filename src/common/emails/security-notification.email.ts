export function securityNotificationEmailTemplate(
  action: string,
  details: string,
  contactSupportText?: string | null,
) {
  return `
  <!doctype html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml"
  xmlns:o="urn:schemas-microsoft-com:office:office">

<head>
  <title>Support Security Time Work</title><!--[if !mso]><!-- -->
  <meta http-equiv="X-UA-Compatible" content="IE=edge"><!--<![endif]-->
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <meta name="viewport" content="width=device-width,initial-scale=1">
  <style type="text/css">
    #outlook a {
      padding: 0;
    }

    .ReadMsgBody {
      width: 100%;
    }

    .ExternalClass {
      width: 100%;
    }

    .ExternalClass * {
      line-height: 100%;
    }

    body {
      margin: 0;
      padding: 0;
      -webkit-text-size-adjust: 100%;
      -ms-text-size-adjust: 100%;
    }
  </style>
</head>

<body>
  <div>
    <table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%">
      <tbody>
        <tr>
          <td style="vertical-align:top;padding:0;">
            <table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%">
              <tr>
                <td align="left" style="font-size:0px;padding:0 0 32px 0;word-break:break-word;">
                  <div
                    style="font-family:Arial, sans-serif;font-size:32px;font-weight:bold;line-height:1;text-align:left;color:#000000;">
                    Security Alert</div>
                </td>
              </tr>
              <tr>
                <td align="left" style="font-size:0px;padding:0;word-break:break-word;">
                  <div
                    style="font-family:Arial, sans-serif;font-size:15px;font-weight:regular;line-height:24px;text-align:left;color:#666666;">
                    We're contacting you to let you know that ${action} on your SSTW account.</div>
                </td>
              </tr>
              <tr>
                <td align="left" style="font-size:0px;padding:16px 0;word-break:break-word;">
                  <div
                    style="font-family:Arial, sans-serif;font-size:15px;font-weight:regular;line-height:24px;text-align:left;color:#666666;">
                    ${details}</div>
                </td>
              </tr>
              ${
                contactSupportText
                  ? `
              <tr>
                <td align="left" style="font-size:0px;padding:16px 0;word-break:break-word;">
                  <div
                    style="font-family:Arial, sans-serif;font-size:15px;font-weight:regular;line-height:24px;text-align:left;color:#666666;">
                    ${contactSupportText}</div>
                </td>
              </tr>
              `
                  : ''
              }
              <tr>
                <td align="left" style="font-size:0px;padding:32px 0;word-break:break-word;">
                  <div
                    style="font-family:Arial, sans-serif;font-size:12px;font-weight:regular;line-height:18px;text-align:left;color:#666666;">
                    Best Regards,<br><b>SSTW Team</b></div>
                </td>
              </tr>
            </table>
          </td>
        </tr>
      </tbody>
    </table>
    <table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%">
      <tbody>
        <tr>
          <td style="background-color:#2D3553;vertical-align:top;padding:32px;">
            <table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%">
              <tr>
                <td align="center" style="font-size:0px;padding:0;word-break:break-word;">
                  <div
                    style="font-family:Arial, sans-serif;font-size:14px;font-weight:300;letter-spacing:0.3px;line-height:20px;text-align:center;color:#ffffff;">
                    <b>Support Security Time Work 2025. All Rights Reserved.</b></div>
                </td>
              </tr>
            </table>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</body>

</html>
`;
}
