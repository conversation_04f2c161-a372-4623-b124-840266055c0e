export const COUNTRIES = [
  {
    value: 'AW',
    call_code: '+297',
    name: 'Aruba',
    emoji: '\ud83c\udde6\ud83c\uddfc',
  },
  {
    value: 'AF',
    call_code: '+93',
    name: 'Afghanistan',
    emoji: '\ud83c\udde6\ud83c\uddeb',
  },
  {
    value: 'AO',
    call_code: '+244',
    name: 'Angola',
    emoji: '\ud83c\udde6\ud83c\uddf4',
  },
  {
    value: 'AI',
    call_code: '+1-264',
    name: '<PERSON><PERSON><PERSON>',
    emoji: '\ud83c\udde6\ud83c\uddee',
  },
  {
    value: 'AX',
    call_code: '+358-18',
    name: '\u00c5land Islands',
    emoji: '\ud83c\udde6\ud83c\uddfd',
  },
  {
    value: 'AL',
    call_code: '+355',
    name: 'Albania',
    emoji: '\ud83c\udde6\ud83c\uddf1',
  },
  {
    value: 'AD',
    call_code: '+376',
    name: 'Andorra',
    emoji: '\ud83c\udde6\ud83c\udde9',
  },
  {
    value: 'AE',
    call_code: '+971',
    name: 'United Arab Emirates',
    emoji: '\ud83c\udde6\ud83c\uddea',
  },
  {
    value: 'AR',
    call_code: '+54',
    name: 'Argentina',
    emoji: '\ud83c\udde6\ud83c\uddf7',
  },
  {
    value: 'AM',
    call_code: '+374',
    name: 'Armenia',
    emoji: '\ud83c\udde6\ud83c\uddf2',
  },
  {
    value: 'AS',
    call_code: '+1-684',
    name: 'American Samoa',
    emoji: '\ud83c\udde6\ud83c\uddf8',
  },
  {
    value: 'AQ',
    call_code: '',
    name: 'Antarctica',
    emoji: '\ud83c\udde6\ud83c\uddf6',
  },
  {
    value: 'TF',
    call_code: '',
    name: 'French Southern Territories',
    emoji: '\ud83c\uddf9\ud83c\uddeb',
  },
  {
    value: 'AG',
    call_code: '+1-268',
    name: 'Antigua and Barbuda',
    emoji: '\ud83c\udde6\ud83c\uddec',
  },
  {
    value: 'AU',
    call_code: '+61',
    name: 'Australia',
    emoji: '\ud83c\udde6\ud83c\uddfa',
  },
  {
    value: 'AT',
    call_code: '+43',
    name: 'Austria',
    emoji: '\ud83c\udde6\ud83c\uddf9',
  },
  {
    value: 'AZ',
    call_code: '+994',
    name: 'Azerbaijan',
    emoji: '\ud83c\udde6\ud83c\uddff',
  },
  {
    value: 'BI',
    call_code: '+257',
    name: 'Burundi',
    emoji: '\ud83c\udde7\ud83c\uddee',
  },
  {
    value: 'BE',
    call_code: '+32',
    name: 'Belgium',
    emoji: '\ud83c\udde7\ud83c\uddea',
  },
  {
    value: 'BJ',
    call_code: '+229',
    name: 'Benin',
    emoji: '\ud83c\udde7\ud83c\uddef',
  },
  {
    value: 'BQ',
    call_code: '+599',
    name: 'Bonaire, Sint Eustatius and Saba',
    emoji: '\ud83c\udde7\ud83c\uddf6',
  },
  {
    value: 'BF',
    call_code: '+226',
    name: 'Burkina Faso',
    emoji: '\ud83c\udde7\ud83c\uddeb',
  },
  {
    value: 'BD',
    call_code: '+880',
    name: 'Bangladesh',
    emoji: '\ud83c\udde7\ud83c\udde9',
  },
  {
    value: 'BG',
    call_code: '+359',
    name: 'Bulgaria',
    emoji: '\ud83c\udde7\ud83c\uddec',
  },
  {
    value: 'BH',
    call_code: '+973',
    name: 'Bahrain',
    emoji: '\ud83c\udde7\ud83c\udded',
  },
  {
    value: 'BS',
    call_code: '+1-242',
    name: 'Bahamas',
    emoji: '\ud83c\udde7\ud83c\uddf8',
  },
  {
    value: 'BA',
    call_code: '+387',
    name: 'Bosnia and Herzegovina',
    emoji: '\ud83c\udde7\ud83c\udde6',
  },
  {
    value: 'BL',
    call_code: '+590',
    name: 'Saint Barth\u00e9lemy',
    emoji: '\ud83c\udde7\ud83c\uddf1',
  },
  {
    value: 'BY',
    call_code: '+375',
    name: 'Belarus',
    emoji: '\ud83c\udde7\ud83c\uddfe',
  },
  {
    value: 'BZ',
    call_code: '+501',
    name: 'Belize',
    emoji: '\ud83c\udde7\ud83c\uddff',
  },
  {
    value: 'BM',
    call_code: '+1-441',
    name: 'Bermuda',
    emoji: '\ud83c\udde7\ud83c\uddf2',
  },
  {
    value: 'BO',
    call_code: '+591',
    name: 'Bolivia, Plurinational State of',
    emoji: '\ud83c\udde7\ud83c\uddf4',
  },
  {
    value: 'BR',
    call_code: '+55',
    name: 'Brazil',
    emoji: '\ud83c\udde7\ud83c\uddf7',
  },
  {
    value: 'BB',
    call_code: '+1-246',
    name: 'Barbados',
    emoji: '\ud83c\udde7\ud83c\udde7',
  },
  {
    value: 'BN',
    call_code: '+673',
    name: 'Brunei Darussalam',
    emoji: '\ud83c\udde7\ud83c\uddf3',
  },
  {
    value: 'BT',
    call_code: '+975',
    name: 'Bhutan',
    emoji: '\ud83c\udde7\ud83c\uddf9',
  },
  {
    value: 'BV',
    call_code: '',
    name: 'Bouvet Island',
    emoji: '\ud83c\udde7\ud83c\uddfb',
  },
  {
    value: 'BW',
    call_code: '+267',
    name: 'Botswana',
    emoji: '\ud83c\udde7\ud83c\uddfc',
  },
  {
    value: 'CF',
    call_code: '+236',
    name: 'Central African Republic',
    emoji: '\ud83c\udde8\ud83c\uddeb',
  },
  {
    value: 'CA',
    call_code: '+1',
    name: 'Canada',
    emoji: '\ud83c\udde8\ud83c\udde6',
  },
  {
    value: 'CC',
    call_code: '+61',
    name: 'Cocos (Keeling) Islands',
    emoji: '\ud83c\udde8\ud83c\udde8',
  },
  {
    value: 'CH',
    call_code: '+41',
    name: 'Switzerland',
    emoji: '\ud83c\udde8\ud83c\udded',
  },
  {
    value: 'CL',
    call_code: '+56',
    name: 'Chile',
    emoji: '\ud83c\udde8\ud83c\uddf1',
  },
  {
    value: 'CN',
    call_code: '+86',
    name: 'China',
    emoji: '\ud83c\udde8\ud83c\uddf3',
  },
  {
    value: 'CI',
    call_code: '+225',
    name: "C\u00f4te d'Ivoire",
    emoji: '\ud83c\udde8\ud83c\uddee',
  },
  {
    value: 'CM',
    call_code: '+237',
    name: 'Cameroon',
    emoji: '\ud83c\udde8\ud83c\uddf2',
  },
  {
    value: 'CD',
    call_code: '+243',
    name: 'Congo, The Democratic Republic of the',
    emoji: '\ud83c\udde8\ud83c\udde9',
  },
  {
    value: 'CG',
    call_code: '+242',
    name: 'Congo',
    emoji: '\ud83c\udde8\ud83c\uddec',
  },
  {
    value: 'CK',
    call_code: '+682',
    name: 'Cook Islands',
    emoji: '\ud83c\udde8\ud83c\uddf0',
  },
  {
    value: 'CO',
    call_code: '+57',
    name: 'Colombia',
    emoji: '\ud83c\udde8\ud83c\uddf4',
  },
  {
    value: 'KM',
    call_code: '+269',
    name: 'Comoros',
    emoji: '\ud83c\uddf0\ud83c\uddf2',
  },
  {
    value: 'CV',
    call_code: '+238',
    name: 'Cabo Verde',
    emoji: '\ud83c\udde8\ud83c\uddfb',
  },
  {
    value: 'CR',
    call_code: '+506',
    name: 'Costa Rica',
    emoji: '\ud83c\udde8\ud83c\uddf7',
  },
  {
    value: 'CU',
    call_code: '+53',
    name: 'Cuba',
    emoji: '\ud83c\udde8\ud83c\uddfa',
  },
  {
    value: 'CW',
    call_code: '+599',
    name: 'Cura\u00e7ao',
    emoji: '\ud83c\udde8\ud83c\uddfc',
  },
  {
    value: 'CX',
    call_code: '+61',
    name: 'Christmas Island',
    emoji: '\ud83c\udde8\ud83c\uddfd',
  },
  {
    value: 'KY',
    call_code: '+1-345',
    name: 'Cayman Islands',
    emoji: '\ud83c\uddf0\ud83c\uddfe',
  },
  {
    value: 'CY',
    call_code: '+357',
    name: 'Cyprus',
    emoji: '\ud83c\udde8\ud83c\uddfe',
  },
  {
    value: 'CZ',
    call_code: '+420',
    name: 'Czechia',
    emoji: '\ud83c\udde8\ud83c\uddff',
  },
  {
    value: 'DE',
    call_code: '+49',
    name: 'Germany',
    emoji: '\ud83c\udde9\ud83c\uddea',
  },
  {
    value: 'DJ',
    call_code: '+253',
    name: 'Djibouti',
    emoji: '\ud83c\udde9\ud83c\uddef',
  },
  {
    value: 'DM',
    call_code: '+1-767',
    name: 'Dominica',
    emoji: '\ud83c\udde9\ud83c\uddf2',
  },
  {
    value: 'DK',
    call_code: '+45',
    name: 'Denmark',
    emoji: '\ud83c\udde9\ud83c\uddf0',
  },
  {
    value: 'DO',
    call_code: '+1-809',
    name: 'Dominican Republic',
    emoji: '\ud83c\udde9\ud83c\uddf4',
  },
  {
    value: 'DZ',
    call_code: '+213',
    name: 'Algeria',
    emoji: '\ud83c\udde9\ud83c\uddff',
  },
  {
    value: 'EC',
    call_code: '+593',
    name: 'Ecuador',
    emoji: '\ud83c\uddea\ud83c\udde8',
  },
  {
    value: 'EG',
    call_code: '+20',
    name: 'Egypt',
    emoji: '\ud83c\uddea\ud83c\uddec',
  },
  {
    value: 'ER',
    call_code: '+291',
    name: 'Eritrea',
    emoji: '\ud83c\uddea\ud83c\uddf7',
  },
  {
    value: 'EH',
    call_code: '+212',
    name: 'Western Sahara',
    emoji: '\ud83c\uddea\ud83c\udded',
  },
  {
    value: 'ES',
    call_code: '+34',
    name: 'Spain',
    emoji: '\ud83c\uddea\ud83c\uddf8',
  },
  {
    value: 'EE',
    call_code: '+372',
    name: 'Estonia',
    emoji: '\ud83c\uddea\ud83c\uddea',
  },
  {
    value: 'ET',
    call_code: '+251',
    name: 'Ethiopia',
    emoji: '\ud83c\uddea\ud83c\uddf9',
  },
  {
    value: 'FI',
    call_code: '+358',
    name: 'Finland',
    emoji: '\ud83c\uddeb\ud83c\uddee',
  },
  {
    value: 'FJ',
    call_code: '+679',
    name: 'Fiji',
    emoji: '\ud83c\uddeb\ud83c\uddef',
  },
  {
    value: 'FK',
    call_code: '+500',
    name: 'Falkland Islands (Malvinas)',
    emoji: '\ud83c\uddeb\ud83c\uddf0',
  },
  {
    value: 'FR',
    call_code: '+33',
    name: 'France',
    emoji: '\ud83c\uddeb\ud83c\uddf7',
  },
  {
    value: 'FO',
    call_code: '+298',
    name: 'Faroe Islands',
    emoji: '\ud83c\uddeb\ud83c\uddf4',
  },
  {
    value: 'FM',
    call_code: '+691',
    name: 'Micronesia, Federated States of',
    emoji: '\ud83c\uddeb\ud83c\uddf2',
  },
  {
    value: 'GA',
    call_code: '+241',
    name: 'Gabon',
    emoji: '\ud83c\uddec\ud83c\udde6',
  },
  {
    value: 'GB',
    call_code: '+44',
    name: 'United Kingdom',
    emoji: '\ud83c\uddec\ud83c\udde7',
  },
  {
    value: 'GE',
    call_code: '+995',
    name: 'Georgia',
    emoji: '\ud83c\uddec\ud83c\uddea',
  },
  {
    value: 'GG',
    call_code: '+44-1481',
    name: 'Guernsey',
    emoji: '\ud83c\uddec\ud83c\uddec',
  },
  {
    value: 'GH',
    call_code: '+233',
    name: 'Ghana',
    emoji: '\ud83c\uddec\ud83c\udded',
  },
  {
    value: 'GI',
    call_code: '+350',
    name: 'Gibraltar',
    emoji: '\ud83c\uddec\ud83c\uddee',
  },
  {
    value: 'GN',
    call_code: '+224',
    name: 'Guinea',
    emoji: '\ud83c\uddec\ud83c\uddf3',
  },
  {
    value: 'GP',
    call_code: '+590',
    name: 'Guadeloupe',
    emoji: '\ud83c\uddec\ud83c\uddf5',
  },
  {
    value: 'GM',
    call_code: '+220',
    name: 'Gambia',
    emoji: '\ud83c\uddec\ud83c\uddf2',
  },
  {
    value: 'GW',
    call_code: '+245',
    name: 'Guinea-Bissau',
    emoji: '\ud83c\uddec\ud83c\uddfc',
  },
  {
    value: 'GQ',
    call_code: '+240',
    name: 'Equatorial Guinea',
    emoji: '\ud83c\uddec\ud83c\uddf6',
  },
  {
    value: 'GR',
    call_code: '+30',
    name: 'Greece',
    emoji: '\ud83c\uddec\ud83c\uddf7',
  },
  {
    value: 'GD',
    call_code: '+1-473',
    name: 'Grenada',
    emoji: '\ud83c\uddec\ud83c\udde9',
  },
  {
    value: 'GL',
    call_code: '+299',
    name: 'Greenland',
    emoji: '\ud83c\uddec\ud83c\uddf1',
  },
  {
    value: 'GT',
    call_code: '+502',
    name: 'Guatemala',
    emoji: '\ud83c\uddec\ud83c\uddf9',
  },
  {
    value: 'GF',
    call_code: '+594',
    name: 'French Guiana',
    emoji: '\ud83c\uddec\ud83c\uddeb',
  },
  {
    value: 'GU',
    call_code: '+1-671',
    name: 'Guam',
    emoji: '\ud83c\uddec\ud83c\uddfa',
  },
  {
    value: 'GY',
    call_code: '+592',
    name: 'Guyana',
    emoji: '\ud83c\uddec\ud83c\uddfe',
  },
  {
    value: 'HK',
    call_code: '+852',
    name: 'Hong Kong',
    emoji: '\ud83c\udded\ud83c\uddf0',
  },
  {
    value: 'HM',
    call_code: '',
    name: 'Heard Island and McDonald Islands',
    emoji: '\ud83c\udded\ud83c\uddf2',
  },
  {
    value: 'HN',
    call_code: '+504',
    name: 'Honduras',
    emoji: '\ud83c\udded\ud83c\uddf3',
  },
  {
    value: 'HR',
    call_code: '+385',
    name: 'Croatia',
    emoji: '\ud83c\udded\ud83c\uddf7',
  },
  {
    value: 'HT',
    call_code: '+509',
    name: 'Haiti',
    emoji: '\ud83c\udded\ud83c\uddf9',
  },
  {
    value: 'HU',
    call_code: '+36',
    name: 'Hungary',
    emoji: '\ud83c\udded\ud83c\uddfa',
  },
  {
    value: 'ID',
    call_code: '+62',
    name: 'Indonesia',
    emoji: '\ud83c\uddee\ud83c\udde9',
  },
  {
    value: 'IM',
    call_code: '+44-1624',
    name: 'Isle of Man',
    emoji: '\ud83c\uddee\ud83c\uddf2',
  },
  {
    value: 'IN',
    call_code: '+91',
    name: 'India',
    emoji: '\ud83c\uddee\ud83c\uddf3',
  },
  {
    value: 'IO',
    call_code: '+246',
    name: 'British Indian Ocean Territory',
    emoji: '\ud83c\uddee\ud83c\uddf4',
  },
  {
    value: 'IE',
    call_code: '+353',
    name: 'Ireland',
    emoji: '\ud83c\uddee\ud83c\uddea',
  },
  {
    value: 'IR',
    call_code: '+98',
    name: 'Iran, Islamic Republic of',
    emoji: '\ud83c\uddee\ud83c\uddf7',
  },
  {
    value: 'IQ',
    call_code: '+964',
    name: 'Iraq',
    emoji: '\ud83c\uddee\ud83c\uddf6',
  },
  {
    value: 'IS',
    call_code: '+354',
    name: 'Iceland',
    emoji: '\ud83c\uddee\ud83c\uddf8',
  },
  {
    value: 'IL',
    call_code: '+972',
    name: 'Israel',
    emoji: '\ud83c\uddee\ud83c\uddf1',
  },
  {
    value: 'IT',
    call_code: '+39',
    name: 'Italy',
    emoji: '\ud83c\uddee\ud83c\uddf9',
  },
  {
    value: 'JM',
    call_code: '+1-876',
    name: 'Jamaica',
    emoji: '\ud83c\uddef\ud83c\uddf2',
  },
  {
    value: 'JE',
    call_code: '+44-1534',
    name: 'Jersey',
    emoji: '\ud83c\uddef\ud83c\uddea',
  },
  {
    value: 'JO',
    call_code: '+962',
    name: 'Jordan',
    emoji: '\ud83c\uddef\ud83c\uddf4',
  },
  {
    value: 'JP',
    call_code: '+81',
    name: 'Japan',
    emoji: '\ud83c\uddef\ud83c\uddf5',
  },
  {
    value: 'KZ',
    call_code: '+7',
    name: 'Kazakhstan',
    emoji: '\ud83c\uddf0\ud83c\uddff',
  },
  {
    value: 'KE',
    call_code: '+254',
    name: 'Kenya',
    emoji: '\ud83c\uddf0\ud83c\uddea',
  },
  {
    value: 'KG',
    call_code: '+996',
    name: 'Kyrgyzstan',
    emoji: '\ud83c\uddf0\ud83c\uddec',
  },
  {
    value: 'KH',
    call_code: '+855',
    name: 'Cambodia',
    emoji: '\ud83c\uddf0\ud83c\udded',
  },
  {
    value: 'KI',
    call_code: '+686',
    name: 'Kiribati',
    emoji: '\ud83c\uddf0\ud83c\uddee',
  },
  {
    value: 'KN',
    call_code: '+1-869',
    name: 'Saint Kitts and Nevis',
    emoji: '\ud83c\uddf0\ud83c\uddf3',
  },
  {
    value: 'KR',
    call_code: '+82',
    name: 'Korea, Republic of',
    emoji: '\ud83c\uddf0\ud83c\uddf7',
  },
  {
    value: 'KW',
    call_code: '+965',
    name: 'Kuwait',
    emoji: '\ud83c\uddf0\ud83c\uddfc',
  },
  {
    value: 'LA',
    call_code: '+856',
    name: "Lao People's Democratic Republic",
    emoji: '\ud83c\uddf1\ud83c\udde6',
  },
  {
    value: 'LB',
    call_code: '+961',
    name: 'Lebanon',
    emoji: '\ud83c\uddf1\ud83c\udde7',
  },
  {
    value: 'LR',
    call_code: '+231',
    name: 'Liberia',
    emoji: '\ud83c\uddf1\ud83c\uddf7',
  },
  {
    value: 'LY',
    call_code: '+218',
    name: 'Libya',
    emoji: '\ud83c\uddf1\ud83c\uddfe',
  },
  {
    value: 'LC',
    call_code: '+1-758',
    name: 'Saint Lucia',
    emoji: '\ud83c\uddf1\ud83c\udde8',
  },
  {
    value: 'LI',
    call_code: '+423',
    name: 'Liechtenstein',
    emoji: '\ud83c\uddf1\ud83c\uddee',
  },
  {
    value: 'LK',
    call_code: '+94',
    name: 'Sri Lanka',
    emoji: '\ud83c\uddf1\ud83c\uddf0',
  },
  {
    value: 'LS',
    call_code: '+266',
    name: 'Lesotho',
    emoji: '\ud83c\uddf1\ud83c\uddf8',
  },
  {
    value: 'LT',
    call_code: '+370',
    name: 'Lithuania',
    emoji: '\ud83c\uddf1\ud83c\uddf9',
  },
  {
    value: 'LU',
    call_code: '+352',
    name: 'Luxembourg',
    emoji: '\ud83c\uddf1\ud83c\uddfa',
  },
  {
    value: 'LV',
    call_code: '+371',
    name: 'Latvia',
    emoji: '\ud83c\uddf1\ud83c\uddfb',
  },
  {
    value: 'MO',
    call_code: '+853',
    name: 'Macao',
    emoji: '\ud83c\uddf2\ud83c\uddf4',
  },
  {
    value: 'MF',
    call_code: '+590',
    name: 'Saint Martin (French part)',
    emoji: '\ud83c\uddf2\ud83c\uddeb',
  },
  {
    value: 'MA',
    call_code: '+212',
    name: 'Morocco',
    emoji: '\ud83c\uddf2\ud83c\udde6',
  },
  {
    value: 'MC',
    call_code: '+377',
    name: 'Monaco',
    emoji: '\ud83c\uddf2\ud83c\udde8',
  },
  {
    value: 'MD',
    call_code: '+373',
    name: 'Moldova, Republic of',
    emoji: '\ud83c\uddf2\ud83c\udde9',
  },
  {
    value: 'MG',
    call_code: '+261',
    name: 'Madagascar',
    emoji: '\ud83c\uddf2\ud83c\uddec',
  },
  {
    value: 'MV',
    call_code: '+960',
    name: 'Maldives',
    emoji: '\ud83c\uddf2\ud83c\uddfb',
  },
  {
    value: 'MX',
    call_code: '+52',
    name: 'Mexico',
    emoji: '\ud83c\uddf2\ud83c\uddfd',
  },
  {
    value: 'MH',
    call_code: '+692',
    name: 'Marshall Islands',
    emoji: '\ud83c\uddf2\ud83c\udded',
  },
  {
    value: 'MK',
    call_code: '+389',
    name: 'North Macedonia',
    emoji: '\ud83c\uddf2\ud83c\uddf0',
  },
  {
    value: 'ML',
    call_code: '+223',
    name: 'Mali',
    emoji: '\ud83c\uddf2\ud83c\uddf1',
  },
  {
    value: 'MT',
    call_code: '+356',
    name: 'Malta',
    emoji: '\ud83c\uddf2\ud83c\uddf9',
  },
  {
    value: 'MM',
    call_code: '+95',
    name: 'Myanmar',
    emoji: '\ud83c\uddf2\ud83c\uddf2',
  },
  {
    value: 'ME',
    call_code: '+382',
    name: 'Montenegro',
    emoji: '\ud83c\uddf2\ud83c\uddea',
  },
  {
    value: 'MN',
    call_code: '+976',
    name: 'Mongolia',
    emoji: '\ud83c\uddf2\ud83c\uddf3',
  },
  {
    value: 'MP',
    call_code: '+1-670',
    name: 'Northern Mariana Islands',
    emoji: '\ud83c\uddf2\ud83c\uddf5',
  },
  {
    value: 'MZ',
    call_code: '+258',
    name: 'Mozambique',
    emoji: '\ud83c\uddf2\ud83c\uddff',
  },
  {
    value: 'MR',
    call_code: '+222',
    name: 'Mauritania',
    emoji: '\ud83c\uddf2\ud83c\uddf7',
  },
  {
    value: 'MS',
    call_code: '+1-664',
    name: 'Montserrat',
    emoji: '\ud83c\uddf2\ud83c\uddf8',
  },
  {
    value: 'MQ',
    call_code: '+596',
    name: 'Martinique',
    emoji: '\ud83c\uddf2\ud83c\uddf6',
  },
  {
    value: 'MU',
    call_code: '+230',
    name: 'Mauritius',
    emoji: '\ud83c\uddf2\ud83c\uddfa',
  },
  {
    value: 'MW',
    call_code: '+265',
    name: 'Malawi',
    emoji: '\ud83c\uddf2\ud83c\uddfc',
  },
  {
    value: 'MY',
    call_code: '+60',
    name: 'Malaysia',
    emoji: '\ud83c\uddf2\ud83c\uddfe',
  },
  {
    value: 'YT',
    call_code: '+262',
    name: 'Mayotte',
    emoji: '\ud83c\uddfe\ud83c\uddf9',
  },
  {
    value: 'NA',
    call_code: '+264',
    name: 'Namibia',
    emoji: '\ud83c\uddf3\ud83c\udde6',
  },
  {
    value: 'NC',
    call_code: '+687',
    name: 'New Caledonia',
    emoji: '\ud83c\uddf3\ud83c\udde8',
  },
  {
    value: 'NE',
    call_code: '+227',
    name: 'Niger',
    emoji: '\ud83c\uddf3\ud83c\uddea',
  },
  {
    value: 'NF',
    call_code: '+672',
    name: 'Norfolk Island',
    emoji: '\ud83c\uddf3\ud83c\uddeb',
  },
  {
    value: 'NG',
    call_code: '+234',
    name: 'Nigeria',
    emoji: '\ud83c\uddf3\ud83c\uddec',
  },
  {
    value: 'NI',
    call_code: '+505',
    name: 'Nicaragua',
    emoji: '\ud83c\uddf3\ud83c\uddee',
  },
  {
    value: 'NU',
    call_code: '+683',
    name: 'Niue',
    emoji: '\ud83c\uddf3\ud83c\uddfa',
  },
  {
    value: 'NL',
    call_code: '+31',
    name: 'Netherlands',
    emoji: '\ud83c\uddf3\ud83c\uddf1',
  },
  {
    value: 'NO',
    call_code: '+47',
    name: 'Norway',
    emoji: '\ud83c\uddf3\ud83c\uddf4',
  },
  {
    value: 'NP',
    call_code: '+977',
    name: 'Nepal',
    emoji: '\ud83c\uddf3\ud83c\uddf5',
  },
  {
    value: 'NR',
    call_code: '+674',
    name: 'Nauru',
    emoji: '\ud83c\uddf3\ud83c\uddf7',
  },
  {
    value: 'NZ',
    call_code: '+64',
    name: 'New Zealand',
    emoji: '\ud83c\uddf3\ud83c\uddff',
  },
  {
    value: 'OM',
    call_code: '+968',
    name: 'Oman',
    emoji: '\ud83c\uddf4\ud83c\uddf2',
  },
  {
    value: 'PK',
    call_code: '+92',
    name: 'Pakistan',
    emoji: '\ud83c\uddf5\ud83c\uddf0',
  },
  {
    value: 'PA',
    call_code: '+507',
    name: 'Panama',
    emoji: '\ud83c\uddf5\ud83c\udde6',
  },
  {
    value: 'PN',
    call_code: '+64',
    name: 'Pitcairn',
    emoji: '\ud83c\uddf5\ud83c\uddf3',
  },
  {
    value: 'PE',
    call_code: '+51',
    name: 'Peru',
    emoji: '\ud83c\uddf5\ud83c\uddea',
  },
  {
    value: 'PH',
    call_code: '+63',
    name: 'Philippines',
    emoji: '\ud83c\uddf5\ud83c\udded',
  },
  {
    value: 'PW',
    call_code: '+680',
    name: 'Palau',
    emoji: '\ud83c\uddf5\ud83c\uddfc',
  },
  {
    value: 'PG',
    call_code: '+675',
    name: 'Papua New Guinea',
    emoji: '\ud83c\uddf5\ud83c\uddec',
  },
  {
    value: 'PL',
    call_code: '+48',
    name: 'Poland',
    emoji: '\ud83c\uddf5\ud83c\uddf1',
  },
  {
    value: 'PR',
    call_code: '+1-787',
    name: 'Puerto Rico',
    emoji: '\ud83c\uddf5\ud83c\uddf7',
  },
  {
    value: 'KP',
    call_code: '+850',
    name: "Korea, Democratic People's Republic of",
    emoji: '\ud83c\uddf0\ud83c\uddf5',
  },
  {
    value: 'PT',
    call_code: '+351',
    name: 'Portugal',
    emoji: '\ud83c\uddf5\ud83c\uddf9',
  },
  {
    value: 'PY',
    call_code: '+595',
    name: 'Paraguay',
    emoji: '\ud83c\uddf5\ud83c\uddfe',
  },
  {
    value: 'PS',
    call_code: '+970',
    name: 'Palestine, State of',
    emoji: '\ud83c\uddf5\ud83c\uddf8',
  },
  {
    value: 'PF',
    call_code: '+689',
    name: 'French Polynesia',
    emoji: '\ud83c\uddf5\ud83c\uddeb',
  },
  {
    value: 'QA',
    call_code: '+974',
    name: 'Qatar',
    emoji: '\ud83c\uddf6\ud83c\udde6',
  },
  {
    value: 'RE',
    call_code: '+262',
    name: 'R\u00e9union',
    emoji: '\ud83c\uddf7\ud83c\uddea',
  },
  {
    value: 'RO',
    call_code: '+40',
    name: 'Romania',
    emoji: '\ud83c\uddf7\ud83c\uddf4',
  },
  {
    value: 'RU',
    call_code: '+7',
    name: 'Russian Federation',
    emoji: '\ud83c\uddf7\ud83c\uddfa',
  },
  {
    value: 'RW',
    call_code: '+250',
    name: 'Rwanda',
    emoji: '\ud83c\uddf7\ud83c\uddfc',
  },
  {
    value: 'SA',
    call_code: '+966',
    name: 'Saudi Arabia',
    emoji: '\ud83c\uddf8\ud83c\udde6',
  },
  {
    value: 'SD',
    call_code: '+249',
    name: 'Sudan',
    emoji: '\ud83c\uddf8\ud83c\udde9',
  },
  {
    value: 'SN',
    call_code: '+221',
    name: 'Senegal',
    emoji: '\ud83c\uddf8\ud83c\uddf3',
  },
  {
    value: 'SG',
    call_code: '+65',
    name: 'Singapore',
    emoji: '\ud83c\uddf8\ud83c\uddec',
  },
  {
    value: 'GS',
    call_code: '',
    name: 'South Georgia and the South Sandwich Islands',
    emoji: '\ud83c\uddec\ud83c\uddf8',
  },
  {
    value: 'SH',
    call_code: '+290',
    name: 'Saint Helena, Ascension and Tristan da Cunha',
    emoji: '\ud83c\uddf8\ud83c\udded',
  },
  {
    value: 'SJ',
    call_code: '+47',
    name: 'Svalbard and Jan Mayen',
    emoji: '\ud83c\uddf8\ud83c\uddef',
  },
  {
    value: 'SB',
    call_code: '+677',
    name: 'Solomon Islands',
    emoji: '\ud83c\uddf8\ud83c\udde7',
  },
  {
    value: 'SL',
    call_code: '+232',
    name: 'Sierra Leone',
    emoji: '\ud83c\uddf8\ud83c\uddf1',
  },
  {
    value: 'SV',
    call_code: '+503',
    name: 'El Salvador',
    emoji: '\ud83c\uddf8\ud83c\uddfb',
  },
  {
    value: 'SM',
    call_code: '+378',
    name: 'San Marino',
    emoji: '\ud83c\uddf8\ud83c\uddf2',
  },
  {
    value: 'SO',
    call_code: '+252',
    name: 'Somalia',
    emoji: '\ud83c\uddf8\ud83c\uddf4',
  },
  {
    value: 'PM',
    call_code: '+508',
    name: 'Saint Pierre and Miquelon',
    emoji: '\ud83c\uddf5\ud83c\uddf2',
  },
  {
    value: 'RS',
    call_code: '+381',
    name: 'Serbia',
    emoji: '\ud83c\uddf7\ud83c\uddf8',
  },
  {
    value: 'SS',
    call_code: '+211',
    name: 'South Sudan',
    emoji: '\ud83c\uddf8\ud83c\uddf8',
  },
  {
    value: 'ST',
    call_code: '+239',
    name: 'Sao Tome and Principe',
    emoji: '\ud83c\uddf8\ud83c\uddf9',
  },
  {
    value: 'SR',
    call_code: '+597',
    name: 'Suriname',
    emoji: '\ud83c\uddf8\ud83c\uddf7',
  },
  {
    value: 'SK',
    call_code: '+421',
    name: 'Slovakia',
    emoji: '\ud83c\uddf8\ud83c\uddf0',
  },
  {
    value: 'SI',
    call_code: '+386',
    name: 'Slovenia',
    emoji: '\ud83c\uddf8\ud83c\uddee',
  },
  {
    value: 'SE',
    call_code: '+46',
    name: 'Sweden',
    emoji: '\ud83c\uddf8\ud83c\uddea',
  },
  {
    value: 'SZ',
    call_code: '+268',
    name: 'Eswatini',
    emoji: '\ud83c\uddf8\ud83c\uddff',
  },
  {
    value: 'SX',
    call_code: '+1-721',
    name: 'Sint Maarten (Dutch part)',
    emoji: '\ud83c\uddf8\ud83c\uddfd',
  },
  {
    value: 'SC',
    call_code: '+248',
    name: 'Seychelles',
    emoji: '\ud83c\uddf8\ud83c\udde8',
  },
  {
    value: 'SY',
    call_code: '+963',
    name: 'Syrian Arab Republic',
    emoji: '\ud83c\uddf8\ud83c\uddfe',
  },
  {
    value: 'TC',
    call_code: '+1-649',
    name: 'Turks and Caicos Islands',
    emoji: '\ud83c\uddf9\ud83c\udde8',
  },
  {
    value: 'TD',
    call_code: '+235',
    name: 'Chad',
    emoji: '\ud83c\uddf9\ud83c\udde9',
  },
  {
    value: 'TG',
    call_code: '+228',
    name: 'Togo',
    emoji: '\ud83c\uddf9\ud83c\uddec',
  },
  {
    value: 'TH',
    call_code: '+66',
    name: 'Thailand',
    emoji: '\ud83c\uddf9\ud83c\udded',
  },
  {
    value: 'TJ',
    call_code: '+992',
    name: 'Tajikistan',
    emoji: '\ud83c\uddf9\ud83c\uddef',
  },
  {
    value: 'TK',
    call_code: '+690',
    name: 'Tokelau',
    emoji: '\ud83c\uddf9\ud83c\uddf0',
  },
  {
    value: 'TM',
    call_code: '+993',
    name: 'Turkmenistan',
    emoji: '\ud83c\uddf9\ud83c\uddf2',
  },
  {
    value: 'TL',
    call_code: '+670',
    name: 'Timor-Leste',
    emoji: '\ud83c\uddf9\ud83c\uddf1',
  },
  {
    value: 'TO',
    call_code: '+676',
    name: 'Tonga',
    emoji: '\ud83c\uddf9\ud83c\uddf4',
  },
  {
    value: 'TT',
    call_code: '+1-868',
    name: 'Trinidad and Tobago',
    emoji: '\ud83c\uddf9\ud83c\uddf9',
  },
  {
    value: 'TN',
    call_code: '+216',
    name: 'Tunisia',
    emoji: '\ud83c\uddf9\ud83c\uddf3',
  },
  {
    value: 'TR',
    call_code: '+90',
    name: 'Turkey',
    emoji: '\ud83c\uddf9\ud83c\uddf7',
  },
  {
    value: 'TV',
    call_code: '+688',
    name: 'Tuvalu',
    emoji: '\ud83c\uddf9\ud83c\uddfb',
  },
  {
    value: 'TW',
    call_code: '+886',
    name: 'Taiwan, Province of China',
    emoji: '\ud83c\uddf9\ud83c\uddfc',
  },
  {
    value: 'TZ',
    call_code: '+255',
    name: 'Tanzania, United Republic of',
    emoji: '\ud83c\uddf9\ud83c\uddff',
  },
  {
    value: 'UG',
    call_code: '+256',
    name: 'Uganda',
    emoji: '\ud83c\uddfa\ud83c\uddec',
  },
  {
    value: 'UA',
    call_code: '+380',
    name: 'Ukraine',
    emoji: '\ud83c\uddfa\ud83c\udde6',
  },
  {
    value: 'UM',
    call_code: '+1',
    name: 'United States Minor Outlying Islands',
    emoji: '\ud83c\uddfa\ud83c\uddf2',
  },
  {
    value: 'UY',
    call_code: '+598',
    name: 'Uruguay',
    emoji: '\ud83c\uddfa\ud83c\uddfe',
  },
  {
    value: 'US',
    call_code: '+1',
    name: 'United States',
    emoji: '\ud83c\uddfa\ud83c\uddf8',
  },
  {
    value: 'UZ',
    call_code: '+998',
    name: 'Uzbekistan',
    emoji: '\ud83c\uddfa\ud83c\uddff',
  },
  {
    value: 'VA',
    call_code: '+39-06',
    name: 'Holy See (Vatican City State)',
    emoji: '\ud83c\uddfb\ud83c\udde6',
  },
  {
    value: 'VC',
    call_code: '+1-784',
    name: 'Saint Vincent and the Grenadines',
    emoji: '\ud83c\uddfb\ud83c\udde8',
  },
  {
    value: 'VE',
    call_code: '+58',
    name: 'Venezuela, Bolivarian Republic of',
    emoji: '\ud83c\uddfb\ud83c\uddea',
  },
  {
    value: 'VG',
    call_code: '+1-284',
    name: 'Virgin Islands, British',
    emoji: '\ud83c\uddfb\ud83c\uddec',
  },
  {
    value: 'VI',
    call_code: '+1-340',
    name: 'Virgin Islands, U.S.',
    emoji: '\ud83c\uddfb\ud83c\uddee',
  },
  {
    value: 'VN',
    call_code: '+84',
    name: 'Viet Nam',
    emoji: '\ud83c\uddfb\ud83c\uddf3',
  },
  {
    value: 'VU',
    call_code: '+678',
    name: 'Vanuatu',
    emoji: '\ud83c\uddfb\ud83c\uddfa',
  },
  {
    value: 'WF',
    call_code: '+681',
    name: 'Wallis and Futuna',
    emoji: '\ud83c\uddfc\ud83c\uddeb',
  },
  {
    value: 'WS',
    call_code: '+685',
    name: 'Samoa',
    emoji: '\ud83c\uddfc\ud83c\uddf8',
  },
  {
    value: 'YE',
    call_code: '+967',
    name: 'Yemen',
    emoji: '\ud83c\uddfe\ud83c\uddea',
  },
  {
    value: 'ZA',
    call_code: '+27',
    name: 'South Africa',
    emoji: '\ud83c\uddff\ud83c\udde6',
  },
  {
    value: 'ZM',
    call_code: '+260',
    name: 'Zambia',
    emoji: '\ud83c\uddff\ud83c\uddf2',
  },
  {
    value: 'ZW',
    call_code: '+263',
    name: 'Zimbabwe',
    emoji: '\ud83c\uddff\ud83c\uddfc',
  },
] as const;

export const COUNTRY_CODES = COUNTRIES.map((country) => country.value);
