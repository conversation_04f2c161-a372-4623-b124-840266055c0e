import { z } from 'zod';

import { COUNTRY_CODES } from './constants/countries.constant';

export enum Role {
  Partner = 'partner',
  Worker = 'worker',
  Manager = 'manager',
}

export enum ApprovalState {
  Pending = 'pending',
  Approved = 'approved',
  Rejected = 'rejected',
}

export enum EmploymentStatus {
  Active = 'active',
  Inactive = 'inactive',
  Quit = 'quit',
  Terminated = 'terminated',
}

export const TERMINATED_STATUSES = [
  EmploymentStatus.Quit,
  EmploymentStatus.Terminated,
] as const;

export const CountriesZodEnum = z.enum(COUNTRY_CODES as [string, ...string[]]);
