import { Request } from 'express';

import { RequestUserDto } from '@/modules/auth/dto/request-user.dto';

export interface RequestGenericInterface {
  params?: unknown;
  body?: unknown;
  query?: unknown;
  headers?: unknown;
}

export interface RequestObject<
  RequestGeneric extends RequestGenericInterface = RequestGenericInterface,
> extends Request<{
    Querystring: RequestGeneric['query'];
    Params: RequestGeneric['params'];
    Body: RequestGeneric['body'];
    Headers: RequestGeneric['headers'];
  }> {
  user?: RequestUserDto;
}

export type DeepPartial<T> = T extends object
  ? {
      [P in keyof T]?: DeepPartial<T[P]>;
    }
  : T;

export type ExtendedFastifyRequestWithUser = Request & {
  user: RequestUserDto;
};

export type Maybe<T = string> = T | null;
export type Optional<T = string> = Maybe<T> | undefined;

export type Mutable<Type> = {
  -readonly [Key in keyof Type]: Type[Key];
};

export type ArgsWithUserId<Args> = Args & { userId: string };

declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace Express {
    // eslint-disable-next-line @typescript-eslint/no-empty-object-type
    interface User extends RequestUserDto {}
  }
}

export type Forwarded<T> = T;
