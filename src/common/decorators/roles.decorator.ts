import { SetMetadata } from '@nestjs/common';

import { Role } from '../enums';
import { ManagerPermissionType } from '../permissions/manager-permissions.config';

type RoleSetting =
  | {
      type: Role.Partner;
    }
  | {
      type: Role.Manager;
      settings?: {
        permissionType?: ManagerPermissionType;
      };
    }
  | {
      type: Role.Worker;
      settings?: {
        mustBeEmployed?: boolean;
        mustBeApproved?: boolean;
      };
    };

export type RolesType = Role | RoleSetting;

export const ROLES_KEY = 'roles';
export const Roles = (...roles: RolesType[]) => SetMetadata(ROLES_KEY, roles);
