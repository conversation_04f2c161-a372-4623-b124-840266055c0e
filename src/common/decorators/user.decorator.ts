import { BadRequestException, createParamDecorator } from '@nestjs/common';
import { Request } from 'express';

export const User = createParamDecorator((_data, ctx) => {
  const request: Request = ctx.switchToHttp().getRequest();
  const user = request.user;

  if (!user?.entityId && user?.role !== 'worker') {
    throw new BadRequestException(
      'User does not have a valid entityId. Please log in again.',
    );
  }

  return user;
});
