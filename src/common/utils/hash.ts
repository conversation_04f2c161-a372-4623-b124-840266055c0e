import { hash, verify } from 'argon2';

import env from '@/config/env.config';

export function hashPassword(password: string) {
  return hash(password, {
    parallelism: 1,
    memoryCost: 19456,
    timeCost: 2,
    hashLength: 32,
  });
}

export function verifyPassword(hashedPassword: string, password: string) {
  return verify(hashedPassword, password);
}

const FIXED_SALT = Buffer.from(
  env.HASH_SALT || '24 1d ae f8 a9 06 db fc 58 1d d6 cb 90 65 2b 50 ',
);

export function hashTokenConsistently(token: string) {
  return hash(token, {
    salt: FIXED_SALT,
    parallelism: 1,
    memoryCost: 19456,
    timeCost: 2,
    hashLength: 16,
  });
}
