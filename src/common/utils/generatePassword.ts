const digits = '0123456789';
const lowerCaseAlphabets = 'abcdefghijklmnopqrstuvwxyz';
const upperCaseAlphabets = lowerCaseAlphabets.toUpperCase();
const specialChars = '#!&@';

interface GenerateOptions {
  digits?: boolean;
  lowerCaseAlphabets?: boolean;
  upperCaseAlphabets?: boolean;
  specialChars?: boolean;
}

function getRandomInt(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min)) + min;
}

export function generatePassword(
  length: number = 10,
  options: GenerateOptions = {},
): string {
  const generateOptions: GenerateOptions = {
    digits: options.digits ?? true,
    lowerCaseAlphabets: options.lowerCaseAlphabets ?? true,
    upperCaseAlphabets: options.upperCaseAlphabets ?? true,
    specialChars: options.specialChars ?? true,
  };

  const allowsChars =
    (generateOptions.digits ? digits : '') +
    (generateOptions.lowerCaseAlphabets ? lowerCaseAlphabets : '') +
    (generateOptions.upperCaseAlphabets ? upperCaseAlphabets : '') +
    (generateOptions.specialChars ? specialChars : '');

  let password = '';
  while (password.length < length) {
    const charIndex = getRandomInt(0, allowsChars.length);
    if (
      password.length === 0 &&
      generateOptions.digits &&
      allowsChars[charIndex] === '0'
    ) {
      continue;
    }
    password += allowsChars[charIndex];
  }
  return password;
}
