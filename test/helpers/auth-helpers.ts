import { INestApplication } from '@nestjs/common';
import request from 'supertest';

import { COUNTRIES } from '../../src/common/constants/countries.constant';

// Utility to get a valid country code and call code
const getCountry = () =>
  COUNTRIES.find((c) => c.call_code.startsWith('+')) || COUNTRIES[0];

export async function registerPartner(app: INestApplication, overrides = {}) {
  const country = getCountry();
  const data = {
    firstName: 'Test',
    lastName: 'Partner',
    phoneNumber: country.call_code + '1234567',
    birthDate: '1990-01-01',
    email: `partner${Date.now()}@test.com`,
    countryOfResidence: country.value,
    citizenship: country.value,
    password: 'TestPassword123',
    taxNumber: '1234567890',
    registrationAddress: 'Test Address',
    companyName: 'Test Company',
    ...overrides,
  };
  const res = await request(app.getHttpServer())
    .post('/auth/register/partner')
    .send(data)
    .expect(201);
  return {
    ...res.body,
    credentials: { email: data.email, password: data.password },
  };
}

export async function login(
  app: INestApplication,
  credential: string,
  password: string,
  deviceId = 'test-device',
) {
  const res = await request(app.getHttpServer())
    .post('/auth/login')
    .send({ credential, password, deviceId })
    .expect(200);
  return res.body;
}

export async function loginAsPartner(
  app: INestApplication,
  partner: { credentials: { email: string; password: string } },
) {
  return login(app, partner.credentials.email, partner.credentials.password);
}

export async function generateInviteCode(
  app: INestApplication,
  partnerToken: string,
  role: 'worker' | 'manager',
  name = 'test-invite',
  extra = {},
) {
  const res = await request(app.getHttpServer())
    .post('/codes/generate')
    .set('Authorization', `Bearer ${partnerToken}`)
    .send({
      name: name + '-' + Date.now(),
      role,
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      ...extra,
    })
    .expect(201);
  return res.body.code;
}

export async function registerWorker(
  app: INestApplication,
  inviteCode: string,
  overrides = {},
) {
  const country = getCountry();
  const data = {
    firstName: 'Test',
    lastName: 'Worker',
    phoneNumber: country.call_code + '2345678',
    birthDate: '1991-01-01',
    email: `worker${Date.now()}@test.com`,
    countryOfResidence: country.value,
    citizenship: country.value,
    password: 'TestPassword123',
    registrationCode: inviteCode,
    ...overrides,
  };
  const res = await request(app.getHttpServer())
    .post('/auth/register/worker')
    .send(data)
    .expect(201);
  return {
    ...res.body,
    credentials: { email: data.email, password: data.password },
  };
}

export async function registerManager(
  app: INestApplication,
  inviteCode: string,
  overrides = {},
) {
  const country = getCountry();
  const data = {
    firstName: 'Test',
    lastName: 'Manager',
    phoneNumber: country.call_code + '3456789',
    birthDate: '1992-01-01',
    email: `manager${Date.now()}@test.com`,
    countryOfResidence: country.value,
    citizenship: country.value,
    password: 'TestPassword123',
    registrationCode: inviteCode,
    ...overrides,
  };
  const res = await request(app.getHttpServer())
    .post('/auth/register/manager')
    .send(data)
    .expect(201);
  return {
    ...res.body,
    credentials: { email: data.email, password: data.password },
  };
}

export async function loginAsWorker(
  app: INestApplication,
  worker: { credentials: { email: string; password: string } },
) {
  return login(app, worker.credentials.email, worker.credentials.password);
}

export async function loginAsManager(
  app: INestApplication,
  manager: { credentials: { email: string; password: string } },
) {
  return login(app, manager.credentials.email, manager.credentials.password);
}

export async function approveRegistrationRequest(
  app: INestApplication,
  partnerToken: string,
  userId: string,
) {
  // Get all registration requests
  const res = await request(app.getHttpServer())
    .get('/registration-requests')
    .set('Authorization', `Bearer ${partnerToken}`)
    .expect(200);
  const req = res.body.find(
    (r: any) => r.workerId === userId || r.managerId === userId,
  );
  if (!req) throw new Error('No registration request found for user');
  // Approve
  await request(app.getHttpServer())
    .patch(`/registration-requests/${req.id}/approve`)
    .set('Authorization', `Bearer ${partnerToken}`)
    .expect(200);
}
