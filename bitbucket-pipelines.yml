image: atlassian/default-image:2

pipelines:
  pull-requests:
    dev:
      - step:
          name: Deploy to Development Server on Merge
          deployment: Staging
          script:
            - mkdir -p ~/.ssh
            - echo "$DEPLOY_SSH_KEY" | sed 's/\\n/\n/g' > ~/.ssh/id_rsa
            - chmod 600 ~/.ssh/id_rsa
            - ssh-keyscan -t rsa -p 2222 dev-api.sstw-admin.com >> ~/.ssh/known_hosts
            - ssh -o StrictHostKeyChecking=no -i ~/.ssh/id_rsa -p 2222 <EMAIL> "deploy dev"
    main:
      - step:
          name: Deploy to Production Server on Merge
          deployment: Production
          script:
            - mkdir -p ~/.ssh
            - echo "$DEPLOY_SSH_KEY" | sed 's/\\n/\n/g' > ~/.ssh/id_rsa
            - chmod 600 ~/.ssh/id_rsa
            - ssh-keyscan -t rsa -p 2222 api.sstw-admin.com >> ~/.ssh/known_hosts
            - ssh -o StrictHostKeyChecking=no -i ~/.ssh/id_rsa -p 2222 <EMAIL> "deploy prod"
