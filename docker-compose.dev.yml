version: '3.8'

services:
  api:
    build:
      context: .
      dockerfile: Dockerfile
    image: sstw-api:dev
    ports:
      - '8081:3000'
    secrets:
      - sstw_dev_database_url
      - sstw_dev_jwt_secret
      - sstw_dev_jwt_refresh_token_secret
      - sstw_dev_s3_access_key_id
      - sstw_dev_s3_secret_access_key
      - sstw_dev_firebase_client_email
      - sstw_dev_firebase_private_key
      - sstw_dev_mailer_pass
      - sstw_dev_redis_password
      - sstw_dev_hash_salt
    environment:
      - NODE_ENV=development
      - PORT=3000
      - LOG_LEVEL=debug
      - BASE_URL=https://dev-api.sstw-admin.com
      - HOST=0.0.0.0

      # Database
      - DATABASE_URL_FILE=/run/secrets/sstw_dev_database_url

      # Auth
      - JWT_SECRET_FILE=/run/secrets/sstw_dev_jwt_secret
      - JWT_REFRESH_TOKEN_SECRET_FILE=/run/secrets/sstw_dev_jwt_refresh_token_secret

      # S3
      - S3_BUCKET=sstwappbucket
      - S3_ACCESS_KEY_ID_FILE=/run/secrets/sstw_dev_s3_access_key_id
      - S3_SECRET_ACCESS_KEY_FILE=/run/secrets/sstw_dev_s3_secret_access_key

      # Firebase
      - FIREBASE_PROJECT_ID=sstw-notifications
      - FIREBASE_CLIENT_EMAIL_FILE=/run/secrets/sstw_dev_firebase_client_email
      - FIREBASE_PRIVATE_KEY_FILE=/run/secrets/sstw_dev_firebase_private_key

      # Email
      - MAILER_HOST=smtp.hostinger.com
      - MAILER_PORT=465
      - MAILER_USER=<EMAIL>
      - MAILER_PASS_FILE=/run/secrets/sstw_dev_mailer_pass
      - MAILER_FROM=SSTW <<EMAIL>>

      # Redis
      - REDIS_HOST=redis-19952.c55.eu-central-1-1.ec2.redns.redis-cloud.com
      - REDIS_PORT=19952
      - REDIS_PASSWORD_FILE=/run/secrets/sstw_dev_redis_password

      # Hash
      - HASH_SALT_FILE=/run/secrets/sstw_dev_hash_salt
      - CHECK_VERSION=1.0.2
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:3000']
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 10s
    deploy:
      replicas: 2
      update_config:
        parallelism: 1
        delay: 10s
        order: start-first
      restart_policy:
        condition: on-failure
        max_attempts: 3
        window: 120s
    networks:
      - api-network

networks:
  api-network:
    driver: overlay

secrets:
  sstw_dev_database_url:
    external: true
  sstw_dev_jwt_secret:
    external: true
  sstw_dev_jwt_refresh_token_secret:
    external: true
  sstw_dev_s3_access_key_id:
    external: true
  sstw_dev_s3_secret_access_key:
    external: true
  sstw_dev_firebase_client_email:
    external: true
  sstw_dev_firebase_private_key:
    external: true
  sstw_dev_mailer_pass:
    external: true
  sstw_dev_redis_password:
    external: true
  sstw_dev_hash_salt:
    external: true
