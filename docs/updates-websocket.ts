export const updatesWebsocketDocs = `
# Real-Time Updates (WebSocket API)

This document describes the WebSocket API for receiving real-time updates from the server.

## Connection Details

**Namespace:** \`/updates\`
**Supported Transports:** WebSocket, Polling
**CORS:** Enabled (all origins)

### Heartbeat Configuration
The server uses a heartbeat mechanism to maintain connection health:
- \`pingInterval\`: 25000ms (25 seconds) - How often the server sends ping packets
- \`pingTimeout\`: 10000ms (10 seconds) - How long to wait for a pong response

If a client fails to respond to a ping within the timeout period, the connection will be automatically terminated.

### Connection Example
\`\`\`javascript
const socket = io('/updates', {
  auth: {
    token: 'your-jwt-token'
  },
  // Optional: Override default heartbeat settings
  pingTimeout: 10000,
  pingInterval: 25000
});
\`\`\`

## Authentication

Authentication is required to establish a WebSocket connection. Provide a JWT token in the connection handshake:

\`\`\`javascript
const socket = io('/updates', {
  auth: {
    token: 'your-jwt-token'
  }
});
\`\`\`

If authentication fails, the connection will be rejected.

## Room Structure

### Room Types
- \`user:{userId}\` - Personal user room
- \`worker:{workerId}\` - Worker-specific room
- \`partner:{partnerId}\` - Partner-specific room
- \`project:{projectId}\` - Project-specific room
- \`managers\` - Global managers room

### Automatic Room Assignment

Users are automatically joined to relevant rooms based on their role:

#### Workers
- \`user:{userId}\`
- \`worker:{workerId}\`
- \`project:{projectId}\` (if assigned to a project)

#### Partners
- \`user:{userId}\`
- \`partner:{partnerId}\`
- \`project:{projectId}\` (for all partner's projects)

## Events

### User Updates

#### \`user.updated\`
**Target Rooms:**
- \`user:{userId}\` (user's personal room)
- \`{roleType}:{entityId}\` (role-specific room)
- If user is a worker, also targets:
  - \`worker:{workerId}\`
  - \`project:{projectId}\` (if assigned)
  - \`partner:{partnerId}\` (if associated)

\`\`\`typescript
{
  type: 'user.updated',
  payload: {
    userId: string,
    action?: 'email_changed' | 'phone_changed' | 'personal_info_changed' | 'verification_changed' | string,
    changes?: {
      email?: boolean,
      phoneNumber?: boolean,
      isEmailVerified?: boolean,
      isPhoneVerified?: boolean,
      personalInfo?: boolean
    }
  }
}
\`\`\`

### Worker Updates

#### \`worker.updated\`
**Target Rooms:**
- \`worker:{workerId}\`
- \`user:{userId}\`
- \`project:{projectId}\` (current and/or new project)
- \`partner:{partnerId}\`

\`\`\`typescript
{
  type: 'worker.updated',
  payload: {
    workerId: string,
    projectId?: string | null,
    action?: 'project_assigned' | 'project_unassigned' | 'user_info_changed' | string,
    // Additional fields when action is 'user_info_changed':
    changes?: {
      email?: boolean,
      phoneNumber?: boolean,
      isEmailVerified?: boolean,
      isPhoneVerified?: boolean,
      personalInfo?: boolean
    }
  }
}
\`\`\`

**Actions:**
- \`project_assigned\`: Worker was assigned to a project
- \`project_unassigned\`: Worker was removed from a project
- \`user_info_changed\`: Worker's user information was updated (includes changes to email, phone, verification status, or personal info)

#### \`worker.employment_changed\`
**Target Rooms:**
- \`worker:{workerId}\`
- Associated project and partner rooms

\`\`\`typescript
{
  type: 'worker.employment_changed',
  payload: {
    workerId: string,
    status: 'active' | 'inactive' | 'quit' | 'terminated'
  }
}
\`\`\`

#### \`worker.approval_changed\`
**Target Rooms:**
- \`worker:{workerId}\`
- Associated project and partner rooms

\`\`\`typescript
{
  type: 'worker.approval_changed',
  payload: {
    workerId: string,
    status: 'approved' | 'rejected' | 'pending'
  }
}
\`\`\`

#### \`worker.status_changed\`
**Target Rooms:**
- \`worker:{workerId}\`
- Associated project and partner rooms

\`\`\`typescript
{
  type: 'worker.status_changed',
  payload: {
    workerId: string,
    status: 'started' | 'finished' | 'paused' | 'passive'
  }
}
\`\`\`

#### \`worker.presence_changed\`
**Target Rooms:**
- \`worker:{workerId}\`
- Associated project and partner rooms

\`\`\`typescript
{
  type: 'worker.presence_changed',
  payload: {
    workerId: string,
    status: 'validated' | 'empty' | 'late'
  }
}
\`\`\`

### Project Updates

#### \`project.created\`
**Target Rooms:**
- \`project:{projectId}\` (new room created)
- \`partner:{partnerId}\`
- All relevant partner users are joined to the new project room

\`\`\`typescript
{
  type: 'project.created',
  payload: {
    projectId: string,
    partnerId: string
  }
}
\`\`\`

#### \`project.updated\`
**Target Rooms:**
- \`project:{projectId}\`
- \`partner:{partnerId}\`

\`\`\`typescript
{
  type: 'project.updated',
  payload: {
    projectId: string,
    partnerId: string
  }
}
\`\`\`

#### \`project.deleted\`
**Target Rooms:**
- \`project:{projectId}\` (before room deletion)
- \`partner:{partnerId}\`
- All sockets are removed from the project room after notification

\`\`\`typescript
{
  type: 'project.deleted',
  payload: {
    projectId: string,
    partnerId: string
  }
}
\`\`\`

### Partner Updates

#### \`partner.updated\`
**Target Rooms:**
- \`partner:{partnerId}\`

\`\`\`typescript
{
  type: 'partner.updated',
  payload: {
    partnerId: string
  }
}
\`\`\`

#### \`daily_report.finished\`
**Target Rooms:**
- \`worker:{workerId}\`
- Associated project and partner rooms

\`\`\`typescript
{
  type: 'daily_report.finished',
  payload: {
    reportId: string,
    workerId: string
  }
}
\`\`\`

This event is emitted when a daily report is finished, either by the worker themselves or by their partner. Clients should use this event to refresh their timer/report status.

## Error Handling

The server may emit error events in the following cases:
- Authentication failure
- Invalid token
- Connection timeout
- Server errors

It's recommended to implement proper error handling and reconnection logic in your client application.

## Example Usage

\`\`\`javascript
const socket = io('/updates', {
  auth: {
    token: 'your-jwt-token'
  }
});

// Handle connection
socket.on('connect', () => {
  console.log('Connected to updates service');
});

// Listen for worker updates
socket.on('worker.updated', (payload) => {
  console.log('Worker updated:', payload);
});

// Handle errors
socket.on('connect_error', (error) => {
  console.error('Connection error:', error);
});
\`\`\`

## Best Practices

1. **Room Management**
   - Be aware of room membership lifecycle
   - Handle room joins/leaves appropriately
   - Monitor room event propagation

2. **Connection Management**
   - Implement reconnection logic
   - Handle connection errors gracefully
   - Clean up listeners when disconnecting

3. **Event Handling**
   - Validate payload data before processing
   - Implement error handling for each event type
   - Use TypeScript interfaces for type safety

4. **Security**
   - Always use HTTPS/WSS in production
   - Keep JWT tokens secure
   - Validate all incoming data

5. **Performance**
   - Minimize the number of concurrent connections
   - Clean up unused event listeners
   - Handle reconnection with exponential backoff
   - Consider heartbeat settings:
     - Default values (25s interval, 10s timeout) are suitable for most applications
     - Adjust based on your needs:
       - Lower values: Faster disconnect detection but more network traffic
       - Higher values: Less network traffic but slower disconnect detection
     - Monitor network conditions and adjust accordingly in high-latency environments
`;
